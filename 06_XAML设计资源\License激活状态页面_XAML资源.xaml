<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- License激活/状态页面 XAML 资源字典 -->
    
    <!-- License状态色彩资源 -->
    <SolidColorBrush x:Key="LicenseValidBrush" Color="#107C10"/>
    <SolidColorBrush x:Key="LicenseTrialBrush" Color="#FFB900"/>
    <SolidColorBrush x:Key="LicenseExpiredBrush" Color="#D13438"/>
    <SolidColorBrush x:Key="LicenseInvalidBrush" Color="#A19F9D"/>
    
    <SolidColorBrush x:Key="ActivationPendingBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="ActivationProcessingBrush" Color="#40E0FF"/>
    <SolidColorBrush x:Key="ActivationSuccessBrush" Color="#107C10"/>
    <SolidColorBrush x:Key="ActivationFailedBrush" Color="#D13438"/>
    
    <SolidColorBrush x:Key="TrialLicenseBrush" Color="#FFB900"/>
    <SolidColorBrush x:Key="StandardLicenseBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="ProfessionalLicenseBrush" Color="#107C10"/>
    
    <!-- 功能状态色彩 -->
    <SolidColorBrush x:Key="FeatureAvailableBrush" Color="#107C10"/>
    <SolidColorBrush x:Key="FeatureRestrictedBrush" Color="#FFB900"/>
    <SolidColorBrush x:Key="FeatureUnavailableBrush" Color="#D13438"/>
    <SolidColorBrush x:Key="FeatureUpgradeBrush" Color="#0078D4"/>

    <!-- 渐变资源 -->
    <LinearGradientBrush x:Key="ActivationButtonGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#0078D4" Offset="0"/>
        <GradientStop Color="#40E0FF" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="UpgradePromptGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#0078D4" Offset="0"/>
        <GradientStop Color="#40E0FF" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="ActivationProgressGradientBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Color="#0078D4" Offset="0"/>
        <GradientStop Color="#40E0FF" Offset="1"/>
    </LinearGradientBrush>

    <!-- 动画资源 -->
    <Storyboard x:Key="PageStateTransitionAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                         From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                         From="20" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="SuccessAnimationStoryboard">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                         From="0" To="1" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)" 
                         From="30" To="0" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleX)" 
                         From="0.9" To="1" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)" 
                         From="0.9" To="1" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="ProcessingPulseAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         From="1" To="1.05" Duration="0:0:2" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         From="1" To="1.05" Duration="0:0:2" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                         From="1" To="0.8" Duration="0:0:2" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="SuccessPulseAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         From="1" To="1.1" Duration="0:0:0.6" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         From="1" To="1.1" Duration="0:0:0.6" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="FailedShakeAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                         Values="0;-3;3;-3;3;0" Duration="0:0:0.5">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="ProgressShimmerAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                         From="-100" To="100" Duration="0:0:1.5">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 样式定义 -->
    
    <!-- License激活窗口样式 -->
    <Style x:Key="LicenseActivationWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundGradientBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="WindowStartupLocation" Value="CenterScreen"/>
        <Setter Property="Width" Value="800"/>
        <Setter Property="Height" Value="700"/>
        <Setter Property="MinWidth" Value="600"/>
        <Setter Property="MinHeight" Value="500"/>
    </Style>

    <!-- 页面标题样式 -->
    <Style x:Key="PageTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>

    <Style x:Key="PageSubtitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>

    <!-- License状态图标样式 -->
    <Style x:Key="LicenseStatusIconStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="80"/>
        <Setter Property="Height" Value="80"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,0,24"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="LicenseStatusIconPendingStyle" TargetType="Ellipse" BasedOn="{StaticResource LicenseStatusIconStyle}">
        <Setter Property="Fill" Value="{StaticResource ActivationPendingBrush}"/>
        <Setter Property="Opacity" Value="0.1"/>
        <Setter Property="Stroke" Value="{StaticResource ActivationPendingBrush}"/>
        <Setter Property="StrokeThickness" Value="3"/>
    </Style>

    <Style x:Key="LicenseStatusIconProcessingStyle" TargetType="Ellipse" BasedOn="{StaticResource LicenseStatusIconStyle}">
        <Setter Property="Fill" Value="{StaticResource ActivationProcessingBrush}"/>
        <Setter Property="Opacity" Value="0.1"/>
        <Setter Property="Stroke" Value="{StaticResource ActivationProcessingBrush}"/>
        <Setter Property="StrokeThickness" Value="3"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource ProcessingPulseAnimation}"/>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="LicenseStatusIconSuccessStyle" TargetType="Ellipse" BasedOn="{StaticResource LicenseStatusIconStyle}">
        <Setter Property="Fill" Value="{StaticResource ActivationSuccessBrush}"/>
        <Setter Property="Opacity" Value="0.1"/>
        <Setter Property="Stroke" Value="{StaticResource ActivationSuccessBrush}"/>
        <Setter Property="StrokeThickness" Value="3"/>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource SuccessPulseAnimation}"/>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="LicenseStatusIconFailedStyle" TargetType="Ellipse" BasedOn="{StaticResource LicenseStatusIconStyle}">
        <Setter Property="Fill" Value="{StaticResource ActivationFailedBrush}"/>
        <Setter Property="Opacity" Value="0.1"/>
        <Setter Property="Stroke" Value="{StaticResource ActivationFailedBrush}"/>
        <Setter Property="StrokeThickness" Value="3"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TranslateTransform/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource FailedShakeAnimation}"/>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- 激活码输入框样式 -->
    <Style x:Key="ActivationCodeInputStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFontFamily}"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Height" Value="56"/>
        <Setter Property="Padding" Value="16,0"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderSecondaryBrush}"/>
        <Setter Property="Background" Value="{StaticResource BackgroundPrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="CharacterCasing" Value="Upper"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <ScrollViewer x:Name="PART_ContentHost"
                                      Margin="{TemplateBinding Padding}"
                                      VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource BorderFocusBrush}"/>
                            <Setter Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="#0078D4" Opacity="0.05"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="Tag" Value="Valid">
                            <Setter Property="BorderBrush" Value="{StaticResource LicenseValidBrush}"/>
                            <Setter Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="#107C10" Opacity="0.05"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="Tag" Value="Invalid">
                            <Setter Property="BorderBrush" Value="{StaticResource ActivationFailedBrush}"/>
                            <Setter Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="#D13438" Opacity="0.05"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 激活按钮样式 -->
    <Style x:Key="ActivationButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource ActivationButtonGradientBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            ClipToBounds="True">
                        <Grid>
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                            <Rectangle x:Name="ShimmerEffect"
                                       Fill="White"
                                       Opacity="0.2"
                                       Width="100"
                                       Height="100"
                                       RenderTransform="{StaticResource ShimmerTransform}"
                                       Visibility="Collapsed"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ShimmerEffect" Property="Visibility" Value="Visible"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="ShimmerEffect"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                                                         From="-100" To="100" Duration="0:0:0.5"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource TextDisabledBrush}"/>
                            <Setter Property="Cursor" Value="Arrow"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- License信息卡片样式 -->
    <Style x:Key="LicenseInfoCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource BackgroundPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="0,24"/>
        <Setter Property="Effect" Value="{StaticResource CardShadowEffect}"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TranslateTransform/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- License类型徽章样式 -->
    <Style x:Key="LicenseTypeBadgeStyle" TargetType="Border">
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <Style x:Key="TrialBadgeStyle" TargetType="Border" BasedOn="{StaticResource LicenseTypeBadgeStyle}">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#FFB900" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush">
            <Setter.Value>
                <SolidColorBrush Color="#FFB900" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <Style x:Key="StandardBadgeStyle" TargetType="Border" BasedOn="{StaticResource LicenseTypeBadgeStyle}">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#0078D4" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush">
            <Setter.Value>
                <SolidColorBrush Color="#0078D4" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <Style x:Key="ProfessionalBadgeStyle" TargetType="Border" BasedOn="{StaticResource LicenseTypeBadgeStyle}">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#107C10" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush">
            <Setter.Value>
                <SolidColorBrush Color="#107C10" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="ActivationProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="4"/>
        <Setter Property="Background" Value="#E1E1E1"/>
        <Setter Property="Foreground" Value="{StaticResource ActivationProgressGradientBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="2"
                            ClipToBounds="True">
                        <Rectangle Name="PART_Track" Fill="{TemplateBinding Background}"/>
                        <Rectangle Name="PART_Indicator" 
                                   Fill="{TemplateBinding Foreground}"
                                   HorizontalAlignment="Left"
                                   CornerRadius="2">
                            <Rectangle.RenderTransform>
                                <TranslateTransform x:Name="ShimmerTransform"/>
                            </Rectangle.RenderTransform>
                        </Rectangle>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsIndeterminate" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ProgressShimmerAnimation}"/>
                            </Trigger.EnterActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 设备指纹容器样式 -->
    <Style x:Key="DeviceFingerprintContainerStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource BackgroundSecondaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="0,24"/>
    </Style>

    <!-- 转换器 -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    
    <!-- 变换资源 -->
    <TranslateTransform x:Key="ShimmerTransform"/>

</ResourceDictionary>
