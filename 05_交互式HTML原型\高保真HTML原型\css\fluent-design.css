/* Fluent Design System 基础样式 */

/* CSS变量定义 */
:root {
    /* 主色调 - 工业蓝 */
    --primary-color: #0078D4;
    --primary-light: #40E0FF;
    --primary-dark: #005A9E;
    
    /* 次要色调 - 暖橙色 */
    --secondary-color: #FF8C00;
    --secondary-light: #FFB347;
    --secondary-dark: #CC7000;
    
    /* 强调色 - 成功绿 */
    --accent-color: #107C10;
    --accent-light: #54B054;
    --accent-dark: #0B5A0B;
    
    /* 状态色彩 */
    --success-color: #107C10;
    --warning-color: #FFB900;
    --error-color: #D13438;
    --info-color: #0078D4;
    
    /* 进度指示色彩 */
    --progress-bg: #E1E1E1;
    --progress-fill: #0078D4;
    --progress-success: #107C10;
    
    /* 文本色彩 */
    --text-primary: #323130;
    --text-secondary: #605E5C;
    --text-disabled: #A19F9D;
    --text-on-primary: #FFFFFF;
    
    /* 背景色彩 */
    --bg-primary: #FFFFFF;
    --bg-secondary: #F3F2F1;
    --bg-tertiary: #EDEBE9;
    --bg-overlay: rgba(0,0,0,0.4);
    
    /* 边框色彩 */
    --border-primary: #EDEBE9;
    --border-secondary: #E1DFDD;
    --border-focus: #0078D4;
    
    /* 阴影 */
    --shadow-small: 0 1px 2px rgba(0,0,0,0.1);
    --shadow-medium: 0 2px 8px rgba(0,0,0,0.15);
    --shadow-large: 0 8px 32px rgba(0,0,0,0.2);
    
    /* 圆角 */
    --radius-small: 2px;
    --radius-medium: 4px;
    --radius-large: 8px;
    --radius-xlarge: 16px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
    
    /* 缓动函数 */
    --ease-standard: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-decelerate: cubic-bezier(0, 0, 0.2, 1);
    --ease-accelerate: cubic-bezier(0.4, 0, 1, 1);
}

/* Fluent Design 基础组件 */

/* 按钮样式 */
.fluent-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-medium);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-standard);
    user-select: none;
    outline: none;
    position: relative;
    overflow: hidden;
}

.fluent-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity var(--duration-fast) var(--ease-standard);
}

.fluent-button:hover::before {
    opacity: 1;
}

.fluent-button:active {
    transform: scale(0.98);
}

.fluent-button:focus {
    box-shadow: 0 0 0 2px var(--border-focus);
}

/* 主要按钮 */
.fluent-button-primary {
    background: var(--primary-color);
    color: var(--text-on-primary);
}

.fluent-button-primary:hover {
    background: var(--primary-dark);
    box-shadow: var(--shadow-medium);
}

/* 次要按钮 */
.fluent-button-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--border-primary);
}

.fluent-button-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

/* 文本按钮 */
.fluent-button-text {
    background: transparent;
    color: var(--primary-color);
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
}

.fluent-button-text:hover {
    background: rgba(0, 120, 212, 0.1);
}

/* 卡片样式 */
.fluent-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-small);
    transition: all var(--duration-normal) var(--ease-standard);
}

.fluent-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-1px);
}

.fluent-card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
}

.fluent-card-content {
    padding: var(--spacing-lg);
}

.fluent-card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    background: var(--bg-secondary);
    border-radius: 0 0 var(--radius-large) var(--radius-large);
}

/* 输入框样式 */
.fluent-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-medium);
    font-size: 14px;
    line-height: 20px;
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: all var(--duration-normal) var(--ease-standard);
    outline: none;
}

.fluent-input:focus {
    border-color: var(--border-focus);
    box-shadow: 0 0 0 1px var(--border-focus);
}

.fluent-input:disabled {
    background: var(--bg-tertiary);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.fluent-input::placeholder {
    color: var(--text-secondary);
}

/* 进度条样式 */
.fluent-progress {
    width: 100%;
    height: 4px;
    background: var(--progress-bg);
    border-radius: var(--radius-small);
    overflow: hidden;
    position: relative;
}

.fluent-progress-bar {
    height: 100%;
    background: var(--progress-fill);
    border-radius: var(--radius-small);
    transition: width var(--duration-slow) var(--ease-standard);
    position: relative;
}

.fluent-progress-indeterminate .fluent-progress-bar {
    width: 30%;
    animation: progressIndeterminate 2s infinite var(--ease-standard);
}

@keyframes progressIndeterminate {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(400%); }
}

/* 状态指示器 */
.fluent-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-medium);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.fluent-status-success {
    background: rgba(16, 124, 16, 0.1);
    color: var(--success-color);
}

.fluent-status-warning {
    background: rgba(255, 185, 0, 0.1);
    color: var(--warning-color);
}

.fluent-status-error {
    background: rgba(209, 52, 56, 0.1);
    color: var(--error-color);
}

.fluent-status-info {
    background: rgba(0, 120, 212, 0.1);
    color: var(--info-color);
}

/* 加载动画 */
.fluent-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-primary);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.fluent-tooltip {
    position: relative;
    display: inline-block;
}

.fluent-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--text-on-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-medium);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--duration-normal) var(--ease-standard);
    z-index: 1000;
    margin-bottom: var(--spacing-xs);
}

.fluent-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 分割线 */
.fluent-divider {
    height: 1px;
    background: var(--border-primary);
    border: none;
    margin: var(--spacing-md) 0;
}

.fluent-divider-vertical {
    width: 1px;
    height: 100%;
    background: var(--border-primary);
    margin: 0 var(--spacing-md);
}

/* 徽章 */
.fluent-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 var(--spacing-xs);
    background: var(--primary-color);
    color: var(--text-on-primary);
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    line-height: 1;
}

.fluent-badge-success {
    background: var(--success-color);
}

.fluent-badge-warning {
    background: var(--warning-color);
}

.fluent-badge-error {
    background: var(--error-color);
}

/* 响应式工具类 */
.fluent-hidden {
    display: none !important;
}

.fluent-visible {
    display: block !important;
}

.fluent-flex {
    display: flex !important;
}

.fluent-inline-flex {
    display: inline-flex !important;
}

/* 间距工具类 */
.fluent-m-0 { margin: 0 !important; }
.fluent-m-1 { margin: var(--spacing-xs) !important; }
.fluent-m-2 { margin: var(--spacing-sm) !important; }
.fluent-m-3 { margin: var(--spacing-md) !important; }
.fluent-m-4 { margin: var(--spacing-lg) !important; }
.fluent-m-5 { margin: var(--spacing-xl) !important; }

.fluent-p-0 { padding: 0 !important; }
.fluent-p-1 { padding: var(--spacing-xs) !important; }
.fluent-p-2 { padding: var(--spacing-sm) !important; }
.fluent-p-3 { padding: var(--spacing-md) !important; }
.fluent-p-4 { padding: var(--spacing-lg) !important; }
.fluent-p-5 { padding: var(--spacing-xl) !important; }

/* 文本工具类 */
.fluent-text-primary { color: var(--text-primary) !important; }
.fluent-text-secondary { color: var(--text-secondary) !important; }
.fluent-text-disabled { color: var(--text-disabled) !important; }
.fluent-text-success { color: var(--success-color) !important; }
.fluent-text-warning { color: var(--warning-color) !important; }
.fluent-text-error { color: var(--error-color) !important; }

.fluent-text-center { text-align: center !important; }
.fluent-text-left { text-align: left !important; }
.fluent-text-right { text-align: right !important; }

/* 背景工具类 */
.fluent-bg-primary { background: var(--bg-primary) !important; }
.fluent-bg-secondary { background: var(--bg-secondary) !important; }
.fluent-bg-tertiary { background: var(--bg-tertiary) !important; }

/* 动画工具类 */
.fluent-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-standard);
}

.fluent-slide-up {
    animation: slideUp var(--duration-normal) var(--ease-standard);
}

.fluent-scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-standard);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from { 
        opacity: 0;
        transform: scale(0.9);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-primary: #000000;
        --border-secondary: #000000;
        --text-secondary: #000000;
    }
    
    .fluent-button {
        border-width: 2px;
    }
    
    .fluent-input {
        border-width: 2px;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
