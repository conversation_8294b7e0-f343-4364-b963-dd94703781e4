<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 启动欢迎页面 XAML 资源字典 -->
    
    <!-- 颜色资源 -->
    <SolidColorBrush x:Key="PrimaryColorBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#40E0FF"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#005A9E"/>
    <SolidColorBrush x:Key="SecondaryColorBrush" Color="#FF8C00"/>
    <SolidColorBrush x:Key="AccentColorBrush" Color="#107C10"/>
    <SolidColorBrush x:Key="ErrorColorBrush" Color="#D13438"/>
    <SolidColorBrush x:Key="WarningColorBrush" Color="#FFB900"/>
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#323130"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#605E5C"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="#A19F9D"/>
    <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="#FFFFFF"/>
    
    <SolidColorBrush x:Key="BackgroundPrimaryBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BackgroundSecondaryBrush" Color="#F3F2F1"/>
    <SolidColorBrush x:Key="BackgroundTertiaryBrush" Color="#EDEBE9"/>
    
    <SolidColorBrush x:Key="BorderPrimaryBrush" Color="#EDEBE9"/>
    <SolidColorBrush x:Key="BorderSecondaryBrush" Color="#E1DFDD"/>
    <SolidColorBrush x:Key="BorderFocusBrush" Color="#0078D4"/>

    <!-- 渐变资源 -->
    <LinearGradientBrush x:Key="AppIconGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#0078D4" Offset="0"/>
        <GradientStop Color="#40E0FF" Offset="0.5"/>
        <GradientStop Color="#FF8C00" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="ProgressGradientBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Color="#0078D4" Offset="0"/>
        <GradientStop Color="#40E0FF" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="BackgroundGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#F5F7FA" Offset="0"/>
        <GradientStop Color="#C3CFE2" Offset="1"/>
    </LinearGradientBrush>

    <!-- 字体资源 -->
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI, Microsoft YaHei UI, Microsoft YaHei</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Consolas, Courier New, Microsoft YaHei UI</FontFamily>

    <!-- 动画资源 -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                         From="0" To="1" Duration="0:0:0.8">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                         From="20" To="0" Duration="0:0:0.8">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="LogoFloatAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                         From="0" To="-8" Duration="0:0:3" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="ProgressShimmerAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                         From="-100" To="100" Duration="0:0:2">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="SpinAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)" 
                         From="0" To="360" Duration="0:0:1"/>
    </Storyboard>

    <!-- 样式定义 -->
    
    <!-- 主窗口样式 -->
    <Style x:Key="StartupWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundGradientBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="WindowStartupLocation" Value="CenterScreen"/>
        <Setter Property="Width" Value="800"/>
        <Setter Property="Height" Value="600"/>
        <Setter Property="MinWidth" Value="600"/>
        <Setter Property="MinHeight" Value="400"/>
    </Style>

    <!-- 品牌标题样式 -->
    <Style x:Key="AppTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>

    <Style x:Key="CompanyNameStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
    </Style>

    <Style x:Key="VersionTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryColorBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- 状态文本样式 -->
    <Style x:Key="StatusMessageStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,0,24"/>
        <Setter Property="MinHeight" Value="24"/>
    </Style>

    <Style x:Key="DetailStatusStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Margin" Value="8,0,0,0"/>
    </Style>

    <Style x:Key="FingerprintTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFontFamily}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Background" Value="{StaticResource BackgroundSecondaryBrush}"/>
        <Setter Property="Padding" Value="8,8,12,8"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="CustomProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Background" Value="#E1E1E1"/>
        <Setter Property="Foreground" Value="{StaticResource ProgressGradientBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="4"
                            ClipToBounds="True">
                        <Rectangle Name="PART_Track" Fill="{TemplateBinding Background}"/>
                        <Rectangle Name="PART_Indicator" 
                                   Fill="{TemplateBinding Foreground}"
                                   HorizontalAlignment="Left"
                                   CornerRadius="4">
                            <Rectangle.RenderTransform>
                                <TranslateTransform x:Name="ProgressTransform"/>
                            </Rectangle.RenderTransform>
                        </Rectangle>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryColorBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,10"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryColorBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16,10"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource BackgroundSecondaryBrush}"/>
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryColorBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource BackgroundSecondaryBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,4"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryColorBrush}"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <!-- 错误容器样式 -->
    <Style x:Key="ErrorContainerStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#D13438" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush">
            <Setter.Value>
                <SolidColorBrush Color="#D13438" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="0,0,0,24"/>
    </Style>

    <!-- 成功容器样式 -->
    <Style x:Key="SuccessContainerStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#107C10" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush">
            <Setter.Value>
                <SolidColorBrush Color="#107C10" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="24"/>
    </Style>

    <!-- 设备信息容器样式 -->
    <Style x:Key="DeviceInfoContainerStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#FFFFFF" Opacity="0.8"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush" Value="{StaticResource BorderPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="0,0,0,24"/>
    </Style>

    <!-- 图标样式 -->
    <Style x:Key="StatusIconStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <Style x:Key="LargeIconStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="48"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>

    <!-- 数据模板 -->
    <DataTemplate x:Key="StatusItemTemplate">
        <Border Style="{StaticResource StatusIndicatorStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding Icon}" Style="{StaticResource StatusIconStyle}"/>
                <TextBlock Text="{Binding Message}" Style="{StaticResource DetailStatusStyle}"/>
            </StackPanel>
        </Border>
    </DataTemplate>

    <!-- 控件模板 -->
    <ControlTemplate x:Key="AppIconTemplate" TargetType="ContentControl">
        <Ellipse Width="128" Height="128" 
                 Fill="{StaticResource AppIconGradientBrush}"
                 Effect="{DynamicResource AppIconShadowEffect}">
            <Ellipse.RenderTransform>
                <TranslateTransform x:Name="FloatTransform"/>
            </Ellipse.RenderTransform>
        </Ellipse>
        <ControlTemplate.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource LogoFloatAnimation}"/>
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!-- 效果资源 -->
    <DropShadowEffect x:Key="AppIconShadowEffect" 
                      Color="#0078D4" 
                      BlurRadius="32" 
                      ShadowDepth="8" 
                      Opacity="0.3"/>

    <DropShadowEffect x:Key="CardShadowEffect" 
                      Color="Black" 
                      BlurRadius="8" 
                      ShadowDepth="2" 
                      Opacity="0.15"/>

    <!-- 转换器 -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

</ResourceDictionary>
