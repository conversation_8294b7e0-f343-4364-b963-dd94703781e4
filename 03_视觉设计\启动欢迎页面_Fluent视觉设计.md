# 启动欢迎页面 - Fluent视觉设计规范

## 1. 页面布局设计

### 1.1 整体布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                        启动欢迎页面                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    顶部品牌区域                         │ │
│  │  [公司Logo]  商用HVAC空调监控调试软件  [版本信息]        │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    中央状态区域                         │ │
│  │                                                         │ │
│  │              [大型软件图标/Logo]                        │ │
│  │                                                         │ │
│  │              正在启动系统...                            │ │
│  │         ████████████████░░░░  80%                      │ │
│  │                                                         │ │
│  │              正在验证License...                         │ │
│  │              设备指纹: ABC123...                        │ │
│  │                                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    底部信息区域                         │ │
│  │  © 2024 公司名称. 保留所有权利.        [重试] [退出]     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 响应式布局规范
- **最小分辨率支持：** 1366x768
- **推荐分辨率：** 1920x1080
- **4K分辨率适配：** 3840x2160
- **DPI感知：** 支持100%、125%、150%、200%缩放

## 2. Fluent Design色彩系统

### 2.1 主色彩定义（HVAC专业色彩）
```css
/* 主色调 - 工业蓝 */
--primary-color: #0078D4;           /* Microsoft Blue */
--primary-light: #40E0FF;           /* 浅蓝色 - 冷气效果 */
--primary-dark: #005A9E;            /* 深蓝色 - 专业感 */

/* 次要色调 - 暖橙色 */
--secondary-color: #FF8C00;         /* 暖橙色 - 制热效果 */
--secondary-light: #FFB347;         /* 浅橙色 */
--secondary-dark: #CC7000;          /* 深橙色 */

/* 强调色 - 成功绿 */
--accent-color: #107C10;            /* 成功绿色 */
--accent-light: #54B054;            /* 浅绿色 */
--accent-dark: #0B5A0B;             /* 深绿色 */
```

### 2.2 功能色彩定义
```css
/* 状态色彩 */
--success-color: #107C10;           /* 成功状态 */
--warning-color: #FFB900;           /* 警告状态 */
--error-color: #D13438;             /* 错误状态 */
--info-color: #0078D4;              /* 信息状态 */

/* 进度指示色彩 */
--progress-bg: #E1E1E1;             /* 进度条背景 */
--progress-fill: #0078D4;           /* 进度条填充 */
--progress-success: #107C10;        /* 完成状态 */
```

### 2.3 中性色彩定义
```css
/* 文本色彩 */
--text-primary: #323130;            /* 主要文本 */
--text-secondary: #605E5C;          /* 次要文本 */
--text-disabled: #A19F9D;           /* 禁用文本 */
--text-on-primary: #FFFFFF;         /* 主色上的文本 */

/* 背景色彩 */
--bg-primary: #FFFFFF;              /* 主背景 */
--bg-secondary: #F3F2F1;            /* 次要背景 */
--bg-tertiary: #EDEBE9;             /* 第三级背景 */
--bg-overlay: rgba(0,0,0,0.4);      /* 遮罩背景 */

/* 边框色彩 */
--border-primary: #EDEBE9;          /* 主要边框 */
--border-secondary: #E1DFDD;        /* 次要边框 */
--border-focus: #0078D4;            /* 焦点边框 */
```

## 3. 字体系统设计

### 3.1 字体选择
```css
/* 主字体族 */
font-family: 'Segoe UI', 'Microsoft YaHei UI', 'Microsoft YaHei', 
             'PingFang SC', 'Hiragino Sans GB', sans-serif;

/* 等宽字体（设备指纹显示） */
font-family: 'Consolas', 'Courier New', 'Microsoft YaHei UI', monospace;
```

### 3.2 字体层级定义
```css
/* 标题层级 */
.title-large {
    font-size: 28px;
    font-weight: 600;
    line-height: 36px;
    color: var(--text-primary);
}

.title-medium {
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    color: var(--text-primary);
}

.title-small {
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    color: var(--text-primary);
}

/* 正文层级 */
.body-large {
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    color: var(--text-primary);
}

.body-medium {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: var(--text-primary);
}

.body-small {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: var(--text-secondary);
}

/* 状态文本 */
.status-text {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: var(--text-secondary);
}

.error-text {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: var(--error-color);
}

/* 设备指纹文本 */
.fingerprint-text {
    font-family: 'Consolas', monospace;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: var(--text-secondary);
    letter-spacing: 0.5px;
}
```

## 4. 图标系统设计

### 4.1 主要图标定义
```css
/* 软件主图标 */
.app-icon {
    width: 128px;
    height: 128px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,120,212,0.3);
}

/* 状态图标 */
.status-icon {
    width: 24px;
    height: 24px;
    fill: var(--text-secondary);
}

.success-icon {
    fill: var(--success-color);
}

.error-icon {
    fill: var(--error-color);
}

.loading-icon {
    fill: var(--primary-color);
    animation: spin 1s linear infinite;
}
```

### 4.2 HVAC专用图标
```css
/* 空调系统图标 */
.hvac-icon {
    width: 32px;
    height: 32px;
    fill: var(--primary-color);
}

/* 温度图标 */
.temperature-icon {
    width: 20px;
    height: 20px;
    fill: var(--secondary-color);
}

/* 设备连接图标 */
.device-icon {
    width: 20px;
    height: 20px;
    fill: var(--text-secondary);
}
```

## 5. 组件视觉规范

### 5.1 进度条设计
```css
.progress-container {
    width: 100%;
    height: 8px;
    background-color: var(--progress-bg);
    border-radius: 4px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255,255,255,0.3), 
        transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}
```

### 5.2 按钮设计
```css
/* 主要按钮 */
.button-primary {
    background: var(--primary-color);
    color: var(--text-on-primary);
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.button-primary:hover {
    background: var(--primary-dark);
    box-shadow: 0 2px 8px rgba(0,120,212,0.3);
}

.button-primary:active {
    transform: translateY(1px);
    box-shadow: 0 1px 4px rgba(0,120,212,0.3);
}

/* 次要按钮 */
.button-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.2s ease;
}

.button-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}
```

### 5.3 状态指示器设计
```css
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    background: var(--bg-secondary);
    border-left: 4px solid var(--info-color);
}

.status-indicator.success {
    border-left-color: var(--success-color);
    background: rgba(16,124,16,0.1);
}

.status-indicator.error {
    border-left-color: var(--error-color);
    background: rgba(209,52,56,0.1);
}

.status-indicator.warning {
    border-left-color: var(--warning-color);
    background: rgba(255,185,0,0.1);
}
```

## 6. 动画和过渡效果

### 6.1 页面加载动画
```css
.startup-container {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### 6.2 加载旋转动画
```css
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}
```

### 6.3 状态切换动画
```css
.status-transition {
    transition: all 0.3s ease;
}

.fade-enter {
    opacity: 0;
    transform: translateX(-10px);
}

.fade-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.3s ease;
}
```

## 7. 响应式设计规范

### 7.1 断点定义
```css
/* 小屏幕 (1366x768) */
@media (max-width: 1366px) {
    .app-icon { width: 96px; height: 96px; }
    .title-large { font-size: 24px; }
}

/* 中等屏幕 (1920x1080) */
@media (min-width: 1367px) and (max-width: 1920px) {
    .app-icon { width: 128px; height: 128px; }
}

/* 大屏幕 (4K) */
@media (min-width: 2560px) {
    .app-icon { width: 160px; height: 160px; }
    .title-large { font-size: 32px; }
}
```

### 7.2 DPI缩放适配
```css
/* 125% DPI */
@media (min-resolution: 120dpi) {
    .app-icon { transform: scale(1.25); }
}

/* 150% DPI */
@media (min-resolution: 144dpi) {
    .app-icon { transform: scale(1.5); }
}

/* 200% DPI */
@media (min-resolution: 192dpi) {
    .app-icon { transform: scale(2); }
}
```

## 8. 无障碍设计

### 8.1 对比度标准
- 所有文本与背景对比度 ≥ 4.5:1
- 大文本（18px+）对比度 ≥ 3:1
- 状态指示器对比度 ≥ 3:1

### 8.2 焦点指示
```css
.focusable:focus {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}
```

## 9. 下一步设计重点
1. **交互设计**：设计启动过程的交互流程和用户反馈
2. **HTML原型制作**：制作像素级精确的交互原型
3. **XAML资源生成**：生成WPF XAML样式和模板
