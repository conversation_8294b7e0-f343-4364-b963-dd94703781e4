# License激活/状态页面 - Fluent视觉设计规范

## 1. 页面布局设计

### 1.1 整体布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                    License激活/状态页面                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    顶部导航区域                         │ │
│  │  [返回] License激活                    [帮助] [支持]     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    主要内容区域                         │ │
│  │                                                         │ │
│  │              [License状态图标]                          │ │
│  │                                                         │ │
│  │              License激活                                │ │
│  │              请输入您的License激活码                     │ │
│  │                                                         │ │
│  │         ┌─────────────────────────────────┐             │ │
│  │         │ XXXXX-XXXXX-XXXXX-XXXXX-XXXXX  │             │ │
│  │         └─────────────────────────────────┘             │ │
│  │                                                         │ │
│  │              设备指纹: ABC123...  [复制]                │ │
│  │                                                         │ │
│  │                   [激活License]                         │ │
│  │                                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    底部信息区域                         │ │
│  │  License类型说明 | 功能对比 | 联系技术支持              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 多状态布局适配
```
激活输入状态 (ActivationInput):
┌─────────────────────────────────┐
│        [激活图标]               │
│        License激活              │
│        请输入激活码             │
│    ┌─────────────────────┐      │
│    │ 激活码输入框        │      │
│    └─────────────────────┘      │
│        设备指纹显示             │
│        [激活按钮]               │
└─────────────────────────────────┘

激活中状态 (Activating):
┌─────────────────────────────────┐
│        [加载动画]               │
│        正在激活License...       │
│        请稍候...                │
│        ████████░░░░ 80%         │
└─────────────────────────────────┘

激活成功状态 (ActivationSuccess):
┌─────────────────────────────────┐
│        [成功图标]               │
│        License激活成功！        │
│        License类型: 专业版      │
│        权限级别: 研发人员权限   │
│        [进入主界面]             │
│        3秒后自动跳转...         │
└─────────────────────────────────┘

License状态管理 (LicenseStatus):
┌─────────────────────────────────┐
│        [状态图标]               │
│        License状态管理          │
│    ┌─────────────────────┐      │
│    │ License类型: 试用版 │      │
│    │ 状态: 有效          │      │
│    │ 到期时间: 30天后    │      │
│    │ 权限: 普通用户权限  │      │
│    └─────────────────────┘      │
│        [升级License]            │
│        [刷新状态]               │
└─────────────────────────────────┘
```

## 2. Fluent Design色彩系统扩展

### 2.1 License状态色彩
```css
/* License状态色彩 */
--license-valid: #107C10;           /* 有效License */
--license-trial: #FFB900;           /* 试用版License */
--license-expired: #D13438;         /* 过期License */
--license-invalid: #A19F9D;         /* 无效License */

/* 激活状态色彩 */
--activation-pending: #0078D4;      /* 等待激活 */
--activation-processing: #40E0FF;   /* 激活中 */
--activation-success: #107C10;      /* 激活成功 */
--activation-failed: #D13438;       /* 激活失败 */

/* License类型色彩 */
--trial-license: #FFB900;           /* 试用版 */
--standard-license: #0078D4;        /* 标准版 */
--professional-license: #107C10;    /* 专业版 */
```

### 2.2 功能状态色彩
```css
/* 功能限制色彩 */
--feature-available: #107C10;       /* 功能可用 */
--feature-restricted: #FFB900;      /* 功能受限 */
--feature-unavailable: #D13438;     /* 功能不可用 */
--feature-upgrade: #0078D4;         /* 需要升级 */
```

## 3. 组件视觉规范

### 3.1 激活码输入框设计
```css
.activation-code-input {
    width: 100%;
    max-width: 400px;
    height: 56px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 2px;
    border: 2px solid var(--border-secondary);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.activation-code-input:focus {
    border-color: var(--border-focus);
    box-shadow: 0 0 0 4px rgba(0, 120, 212, 0.2);
    outline: none;
}

.activation-code-input.valid {
    border-color: var(--license-valid);
    background: rgba(16, 124, 16, 0.05);
}

.activation-code-input.invalid {
    border-color: var(--activation-failed);
    background: rgba(209, 52, 56, 0.05);
    animation: inputShake 0.5s ease;
}

@keyframes inputShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 激活码格式化显示 */
.activation-code-display {
    font-family: 'Consolas', monospace;
    font-size: 16px;
    letter-spacing: 1px;
    color: var(--text-primary);
    background: var(--bg-secondary);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-primary);
}
```

### 3.2 License状态卡片设计
```css
.license-status-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.license-status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--license-valid);
}

.license-status-card.trial::before {
    background: var(--license-trial);
}

.license-status-card.expired::before {
    background: var(--license-expired);
}

.license-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.license-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.license-type-badge.trial {
    background: rgba(255, 185, 0, 0.1);
    color: var(--trial-license);
    border: 1px solid rgba(255, 185, 0, 0.3);
}

.license-type-badge.standard {
    background: rgba(0, 120, 212, 0.1);
    color: var(--standard-license);
    border: 1px solid rgba(0, 120, 212, 0.3);
}

.license-type-badge.professional {
    background: rgba(16, 124, 16, 0.1);
    color: var(--professional-license);
    border: 1px solid rgba(16, 124, 16, 0.3);
}
```

### 3.3 设备指纹显示设计
```css
.device-fingerprint-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.device-fingerprint-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.device-fingerprint-value {
    font-family: 'Consolas', monospace;
    font-size: 14px;
    color: var(--text-primary);
    word-break: break-all;
    line-height: 1.4;
    margin-bottom: 12px;
}

.copy-fingerprint-btn {
    background: transparent;
    border: 1px solid var(--border-secondary);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.copy-fingerprint-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.copy-fingerprint-btn:active {
    transform: scale(0.98);
}
```

### 3.4 激活按钮设计
```css
.activation-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--text-on-primary);
    border: none;
    border-radius: 8px;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 160px;
}

.activation-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.activation-button:hover::before {
    left: 100%;
}

.activation-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 120, 212, 0.4);
}

.activation-button:active {
    transform: translateY(0);
}

.activation-button:disabled {
    background: var(--bg-tertiary);
    color: var(--text-disabled);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.activation-button.loading {
    pointer-events: none;
}

.activation-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid var(--text-on-primary);
    border-radius: 50%;
    animation: buttonSpin 1s linear infinite;
}

@keyframes buttonSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

## 4. 状态图标设计

### 4.1 License状态图标
```css
.license-status-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    position: relative;
}

.license-status-icon.pending {
    background: rgba(0, 120, 212, 0.1);
    color: var(--activation-pending);
    border: 2px solid rgba(0, 120, 212, 0.3);
}

.license-status-icon.processing {
    background: rgba(64, 224, 255, 0.1);
    color: var(--activation-processing);
    border: 2px solid rgba(64, 224, 255, 0.3);
    animation: processingPulse 2s ease-in-out infinite;
}

.license-status-icon.success {
    background: rgba(16, 124, 16, 0.1);
    color: var(--activation-success);
    border: 2px solid rgba(16, 124, 16, 0.3);
    animation: successPulse 0.6s ease;
}

.license-status-icon.failed {
    background: rgba(209, 52, 56, 0.1);
    color: var(--activation-failed);
    border: 2px solid rgba(209, 52, 56, 0.3);
    animation: failedShake 0.5s ease;
}

@keyframes processingPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes failedShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}
```

### 4.2 功能限制图标
```css
.feature-restriction-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-right: 8px;
}

.feature-restriction-icon.available {
    background: var(--feature-available);
    color: white;
}

.feature-restriction-icon.restricted {
    background: var(--feature-restricted);
    color: white;
}

.feature-restriction-icon.unavailable {
    background: var(--feature-unavailable);
    color: white;
}
```

## 5. 动画和过渡效果

### 5.1 页面状态切换动画
```css
.page-state-transition {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.state-enter {
    opacity: 0;
    transform: translateY(20px);
}

.state-enter-active {
    opacity: 1;
    transform: translateY(0);
}

.state-exit {
    opacity: 1;
    transform: translateY(0);
}

.state-exit-active {
    opacity: 0;
    transform: translateY(-20px);
}
```

### 5.2 激活进度动画
```css
.activation-progress {
    width: 100%;
    height: 4px;
    background: var(--progress-bg);
    border-radius: 2px;
    overflow: hidden;
    margin: 16px 0;
}

.activation-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
}

.activation-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}
```

## 6. 响应式设计

### 6.1 移动端适配
```css
@media (max-width: 768px) {
    .license-activation-container {
        padding: 16px;
    }
    
    .activation-code-input {
        font-size: 16px;
        height: 48px;
    }
    
    .license-status-card {
        padding: 16px;
    }
    
    .activation-button {
        width: 100%;
        padding: 14px 24px;
    }
}
```

### 6.2 平板端适配
```css
@media (min-width: 769px) and (max-width: 1024px) {
    .license-activation-container {
        max-width: 600px;
        margin: 0 auto;
    }
}
```

## 7. 无障碍设计

### 7.1 高对比度支持
```css
@media (prefers-contrast: high) {
    .activation-code-input {
        border-width: 3px;
    }
    
    .license-status-card {
        border-width: 2px;
    }
    
    .activation-button {
        border: 2px solid var(--text-primary);
    }
}
```

### 7.2 焦点指示
```css
.focusable:focus {
    outline: 3px solid var(--border-focus);
    outline-offset: 2px;
}
```

## 8. 下一步设计重点
1. **交互设计**：设计激活流程的详细交互和用户反馈
2. **HTML原型制作**：制作像素级精确的交互原型
3. **XAML资源生成**：生成WPF XAML样式和模板
