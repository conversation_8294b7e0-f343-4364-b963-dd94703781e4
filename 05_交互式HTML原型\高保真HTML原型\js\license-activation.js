// License激活/状态页面交互逻辑

class LicenseActivationController {
    constructor() {
        this.currentState = 'loading';
        this.activationContext = null;
        this.deviceFingerprint = '';
        this.activationCode = '';
        this.isActivationCodeValid = false;
        this.currentLicense = null;
        this.activationProgress = 0;
        
        this.initializeElements();
        this.bindEvents();
        this.initializePage();
    }

    initializeElements() {
        // 获取DOM元素
        this.elements = {
            // 页面状态容器
            loadingState: document.getElementById('loadingState'),
            activationInputState: document.getElementById('activationInputState'),
            activatingState: document.getElementById('activatingState'),
            activationSuccessState: document.getElementById('activationSuccessState'),
            activationFailedState: document.getElementById('activationFailedState'),
            licenseStatusState: document.getElementById('licenseStatusState'),
            
            // 导航元素
            pageTitle: document.getElementById('pageTitle'),
            pageSubtitle: document.getElementById('pageSubtitle'),
            backButton: document.getElementById('backButton'),
            helpButton: document.getElementById('helpButton'),
            supportButton: document.getElementById('supportButton'),
            
            // 激活表单元素
            activationCodeInput: document.getElementById('activationCodeInput'),
            characterCounter: document.getElementById('characterCounter'),
            validationFeedback: document.getElementById('validationFeedback'),
            deviceFingerprintValue: document.getElementById('deviceFingerprintValue'),
            copyFingerprintBtn: document.getElementById('copyFingerprintBtn'),
            copyTooltip: document.getElementById('copyTooltip'),
            activationButton: document.getElementById('activationButton'),
            statusMessage: document.getElementById('statusMessage'),
            statusIcon: document.getElementById('statusIcon'),
            
            // 激活进度元素
            activatingMessage: document.getElementById('activatingMessage'),
            activationProgressFill: document.getElementById('activationProgressFill'),
            step1: document.getElementById('step1'),
            step2: document.getElementById('step2'),
            step3: document.getElementById('step3'),
            step4: document.getElementById('step4'),
            
            // 成功状态元素
            licenseTypeBadge: document.getElementById('licenseTypeBadge'),
            licenseTypeValue: document.getElementById('licenseTypeValue'),
            permissionLevelValue: document.getElementById('permissionLevelValue'),
            expiryDateValue: document.getElementById('expiryDateValue'),
            proceedButton: document.getElementById('proceedButton'),
            autoRedirect: document.getElementById('autoRedirect'),
            skipCountdownBtn: document.getElementById('skipCountdownBtn'),
            
            // 失败状态元素
            errorMessage: document.getElementById('errorMessage'),
            errorDetails: document.getElementById('errorDetails'),
            retryButton: document.getElementById('retryButton'),
            contactSupportButton: document.getElementById('contactSupportButton'),
            
            // License状态管理元素
            statusLicenseTypeBadge: document.getElementById('statusLicenseTypeBadge'),
            licenseStatusValue: document.getElementById('licenseStatusValue'),
            remainingDaysValue: document.getElementById('remainingDaysValue'),
            statusPermissionValue: document.getElementById('statusPermissionValue'),
            featureRestrictions: document.getElementById('featureRestrictions'),
            upgradePrompt: document.getElementById('upgradePrompt'),
            upgradeButton: document.getElementById('upgradeButton'),
            refreshStatusButton: document.getElementById('refreshStatusButton'),
            statusProceedButton: document.getElementById('statusProceedButton'),
            
            // 底部信息元素
            licenseTypesInfo: document.getElementById('licenseTypesInfo'),
            featureComparisonInfo: document.getElementById('featureComparisonInfo'),
            technicalSupportInfo: document.getElementById('technicalSupportInfo'),
            
            // 模态对话框元素
            modalOverlay: document.getElementById('modalOverlay'),
            modalContent: document.getElementById('modalContent'),
            modalTitle: document.getElementById('modalTitle'),
            modalBody: document.getElementById('modalBody'),
            modalClose: document.getElementById('modalClose'),
            modalCancel: document.getElementById('modalCancel'),
            modalConfirm: document.getElementById('modalConfirm')
        };
    }

    bindEvents() {
        // 导航事件
        this.elements.backButton.addEventListener('click', () => this.goBack());
        this.elements.helpButton.addEventListener('click', () => this.showHelp());
        this.elements.supportButton.addEventListener('click', () => this.showSupport());
        
        // 激活码输入事件
        this.elements.activationCodeInput.addEventListener('input', (e) => this.handleActivationCodeInput(e));
        this.elements.activationCodeInput.addEventListener('paste', (e) => this.handleActivationCodePaste(e));
        this.elements.activationCodeInput.addEventListener('keydown', (e) => this.handleActivationCodeKeydown(e));
        
        // 设备指纹复制事件
        this.elements.copyFingerprintBtn.addEventListener('click', () => this.copyDeviceFingerprint());
        
        // 激活按钮事件
        this.elements.activationButton.addEventListener('click', () => this.activateLicense());
        
        // 成功状态事件
        this.elements.proceedButton.addEventListener('click', () => this.proceedToMainApplication());
        this.elements.skipCountdownBtn.addEventListener('click', () => this.proceedToMainApplication());
        
        // 失败状态事件
        this.elements.retryButton.addEventListener('click', () => this.retryActivation());
        this.elements.contactSupportButton.addEventListener('click', () => this.contactSupport());
        
        // License状态管理事件
        this.elements.upgradeButton.addEventListener('click', () => this.showUpgradeOptions());
        this.elements.refreshStatusButton.addEventListener('click', () => this.refreshLicenseStatus());
        this.elements.statusProceedButton.addEventListener('click', () => this.proceedToMainApplication());
        
        // 底部信息事件
        this.elements.licenseTypesInfo.addEventListener('click', () => this.showLicenseTypesInfo());
        this.elements.featureComparisonInfo.addEventListener('click', () => this.showFeatureComparison());
        this.elements.technicalSupportInfo.addEventListener('click', () => this.showTechnicalSupport());
        
        // 模态对话框事件
        this.elements.modalClose.addEventListener('click', () => this.hideModal());
        this.elements.modalCancel.addEventListener('click', () => this.hideModal());
        this.elements.modalOverlay.addEventListener('click', (e) => {
            if (e.target === this.elements.modalOverlay) {
                this.hideModal();
            }
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
    }

    async initializePage() {
        try {
            // 显示加载状态
            this.showPageState('loading');
            
            // 生成设备指纹
            await this.generateDeviceFingerprint();
            
            // 检查URL参数获取激活上下文
            this.parseActivationContext();
            
            // 根据上下文设置页面状态
            await this.setupPageByContext();
            
        } catch (error) {
            console.error('页面初始化失败:', error);
            this.showActivationInput();
        }
    }

    parseActivationContext() {
        // 从URL参数或localStorage获取激活上下文
        const urlParams = new URLSearchParams(window.location.search);
        const contextData = urlParams.get('context');
        
        if (contextData) {
            try {
                this.activationContext = JSON.parse(decodeURIComponent(contextData));
            } catch (e) {
                console.warn('解析激活上下文失败:', e);
            }
        }
        
        // 如果没有上下文，尝试从localStorage获取
        if (!this.activationContext) {
            const storedContext = localStorage.getItem('licenseActivationContext');
            if (storedContext) {
                try {
                    this.activationContext = JSON.parse(storedContext);
                } catch (e) {
                    console.warn('解析存储的激活上下文失败:', e);
                }
            }
        }
        
        // 设置默认上下文
        if (!this.activationContext) {
            this.activationContext = {
                failureReason: 'not_activated',
                failureMessage: '需要激活License',
                isFirstTimeActivation: true,
                deviceFingerprint: this.deviceFingerprint
            };
        }
    }

    async setupPageByContext() {
        const context = this.activationContext;
        
        // 根据失败原因设置页面标题和消息
        switch (context.failureReason) {
            case 'file_not_found':
                this.elements.pageTitle.textContent = 'License激活';
                this.elements.pageSubtitle.textContent = '首次使用，请输入您的License激活码';
                this.elements.statusMessage.textContent = '欢迎使用商用HVAC空调监控调试软件';
                break;
                
            case 'expired':
                this.elements.pageTitle.textContent = 'License重新激活';
                this.elements.pageSubtitle.textContent = '您的License已过期，请重新激活';
                this.elements.statusMessage.textContent = 'License文件已过期或损坏，需要重新激活';
                break;
                
            case 'device_mismatch':
                this.elements.pageTitle.textContent = 'License重新激活';
                this.elements.pageSubtitle.textContent = '设备验证失败，请在当前设备上重新激活';
                this.elements.statusMessage.textContent = 'License与当前设备不匹配，需要重新激活';
                break;
                
            default:
                this.elements.pageTitle.textContent = 'License激活';
                this.elements.pageSubtitle.textContent = '请输入您的License激活码';
                this.elements.statusMessage.textContent = '请激活License以使用软件功能';
                break;
        }
        
        // 检查是否已有有效License
        const existingLicense = await this.checkExistingLicense();
        
        if (existingLicense && existingLicense.isValid) {
            // 显示License状态管理界面
            this.currentLicense = existingLicense;
            this.showLicenseStatus();
        } else {
            // 显示激活输入界面
            this.showActivationInput();
        }
    }

    async generateDeviceFingerprint() {
        // 模拟设备指纹生成
        await this.delay(500);
        
        // 生成基于浏览器和系统信息的指纹
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        const fingerprint = [
            navigator.userAgent.slice(0, 20),
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            navigator.language,
            canvas.toDataURL().slice(-20)
        ].join('').replace(/[^a-zA-Z0-9]/g, '').toUpperCase().slice(0, 32);
        
        // 格式化为8-8-8-8格式
        this.deviceFingerprint = fingerprint.match(/.{1,8}/g).join('-');
        this.elements.deviceFingerprintValue.textContent = this.deviceFingerprint;
    }

    async checkExistingLicense() {
        // 模拟检查现有License
        await this.delay(300);
        
        const storedLicense = localStorage.getItem('currentLicense');
        if (storedLicense) {
            try {
                const license = JSON.parse(storedLicense);
                // 检查License是否有效且未过期
                if (license.deviceFingerprint === this.deviceFingerprint && 
                    new Date(license.expiryDate) > new Date()) {
                    return license;
                }
            } catch (e) {
                console.warn('解析存储的License失败:', e);
            }
        }
        
        return null;
    }

    showPageState(state) {
        // 隐藏所有状态
        Object.values(this.elements).forEach(element => {
            if (element && element.classList && element.classList.contains('page-state')) {
                element.style.display = 'none';
                element.classList.remove('active');
            }
        });
        
        // 显示指定状态
        const stateElement = this.elements[state + 'State'];
        if (stateElement) {
            stateElement.style.display = 'block';
            setTimeout(() => {
                stateElement.classList.add('active');
            }, 50);
        }
        
        this.currentState = state;
    }

    showActivationInput() {
        this.showPageState('activationInput');
        this.elements.activationCodeInput.focus();
    }

    showLicenseStatus() {
        this.showPageState('licenseStatus');
        this.updateLicenseStatusDisplay();
    }

    // 工具方法
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    formatActivationCode(code) {
        // 移除所有非字母数字字符
        const clean = code.replace(/[^A-Z0-9]/g, '');
        
        // 按5位一组格式化
        const formatted = clean.match(/.{1,5}/g)?.join('-') || clean;
        
        return formatted.slice(0, 29); // 最大长度 XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
    }

    validateActivationCode(code) {
        const clean = code.replace(/[^A-Z0-9]/g, '');

        if (clean.length === 0) {
            return { isValid: false, message: '' };
        }

        if (clean.length < 20) {
            return { isValid: false, message: `还需要 ${20 - clean.length} 个字符` };
        }

        if (clean.length === 20) {
            return { isValid: true, message: '激活码格式正确' };
        }

        return { isValid: false, message: '激活码过长' };
    }

    // 事件处理方法
    handleActivationCodeInput(event) {
        const input = event.target;
        const rawValue = input.value.toUpperCase();

        // 格式化激活码
        const formatted = this.formatActivationCode(rawValue);
        input.value = formatted;
        this.activationCode = formatted;

        // 验证激活码
        const validation = this.validateActivationCode(formatted);
        this.isActivationCodeValid = validation.isValid;

        // 更新字符计数器
        const clean = formatted.replace(/[^A-Z0-9]/g, '');
        this.elements.characterCounter.textContent = `${clean.length}/20`;
        this.elements.characterCounter.classList.toggle('complete', clean.length === 20);

        // 更新验证反馈
        this.elements.validationFeedback.textContent = validation.message;
        this.elements.validationFeedback.className = 'validation-feedback';
        if (validation.message) {
            this.elements.validationFeedback.classList.add(validation.isValid ? 'success' : 'error');
        }

        // 更新输入框样式
        input.className = 'activation-code-input';
        if (clean.length > 0) {
            input.classList.add(validation.isValid ? 'valid' : 'invalid');
        }

        // 更新激活按钮状态
        this.elements.activationButton.disabled = !validation.isValid;
    }

    handleActivationCodePaste(event) {
        event.preventDefault();
        const pastedText = (event.clipboardData || window.clipboardData).getData('text');
        const formatted = this.formatActivationCode(pastedText.toUpperCase());

        event.target.value = formatted;
        this.handleActivationCodeInput(event);
    }

    handleActivationCodeKeydown(event) {
        // 允许的按键
        const allowedKeys = [
            'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
            'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
            'Home', 'End'
        ];

        if (allowedKeys.includes(event.key)) {
            if (event.key === 'Enter' && this.isActivationCodeValid) {
                this.activateLicense();
            }
            return;
        }

        // 只允许字母和数字
        if (!/^[A-Za-z0-9]$/.test(event.key)) {
            event.preventDefault();
        }
    }

    async copyDeviceFingerprint() {
        try {
            await navigator.clipboard.writeText(this.deviceFingerprint);

            // 显示复制成功反馈
            this.elements.copyFingerprintBtn.classList.add('copied');
            this.elements.copyTooltip.classList.add('show');

            setTimeout(() => {
                this.elements.copyFingerprintBtn.classList.remove('copied');
                this.elements.copyTooltip.classList.remove('show');
            }, 2000);

        } catch (err) {
            console.warn('复制到剪贴板失败:', err);
            // 降级方案：选择文本
            const range = document.createRange();
            range.selectNode(this.elements.deviceFingerprintValue);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
        }
    }

    async activateLicense() {
        if (!this.isActivationCodeValid) {
            return;
        }

        try {
            // 显示激活中状态
            this.showPageState('activating');
            this.elements.activationButton.classList.add('loading');

            // 模拟激活过程
            await this.performActivation();

        } catch (error) {
            console.error('激活失败:', error);
            this.showActivationFailure(error.message);
        } finally {
            this.elements.activationButton.classList.remove('loading');
        }
    }

    async performActivation() {
        const steps = [
            { message: '验证激活码...', progress: 25 },
            { message: '生成License文件...', progress: 50 },
            { message: '配置权限设置...', progress: 75 },
            { message: '完成激活...', progress: 100 }
        ];

        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];

            // 更新进度消息
            this.elements.activatingMessage.textContent = step.message;

            // 更新进度条
            this.elements.activationProgressFill.style.width = `${step.progress}%`;

            // 更新步骤状态
            const stepElement = this.elements[`step${i + 1}`];
            stepElement.classList.add('active');

            // 模拟处理时间
            await this.delay(800 + Math.random() * 400);

            stepElement.classList.remove('active');
            stepElement.classList.add('completed');
        }

        // 模拟激活结果
        const success = Math.random() > 0.3; // 70%成功率

        if (success) {
            // 生成模拟License
            this.currentLicense = this.generateMockLicense();

            // 保存到localStorage
            localStorage.setItem('currentLicense', JSON.stringify(this.currentLicense));

            this.showActivationSuccess();
        } else {
            throw new Error('激活码无效或已被使用');
        }
    }

    generateMockLicense() {
        const licenseTypes = ['Trial', 'Standard', 'Professional'];
        const randomType = licenseTypes[Math.floor(Math.random() * licenseTypes.length)];

        const expiryDate = new Date();
        if (randomType === 'Trial') {
            expiryDate.setDate(expiryDate.getDate() + 30); // 试用版30天
        } else {
            expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 其他版本1年
        }

        return {
            licenseKey: this.activationCode,
            licenseType: randomType,
            deviceFingerprint: this.deviceFingerprint,
            issueDate: new Date().toISOString(),
            expiryDate: expiryDate.toISOString(),
            isValid: true,
            companyName: '示例公司',
            productVersion: '1.0.0'
        };
    }

    showActivationSuccess() {
        this.showPageState('activationSuccess');

        // 更新License信息显示
        this.updateSuccessDisplay();

        // 开始自动跳转倒计时
        this.startAutoRedirectCountdown();
    }

    updateSuccessDisplay() {
        const license = this.currentLicense;
        const typeDisplayNames = {
            'Trial': '试用版',
            'Standard': '标准版',
            'Professional': '专业版'
        };

        const permissionDisplayNames = {
            'Trial': '普通用户权限',
            'Standard': '售后服务用户权限',
            'Professional': '研发人员权限'
        };

        const typeDisplay = typeDisplayNames[license.licenseType] || license.licenseType;
        const permissionDisplay = permissionDisplayNames[license.licenseType] || '未知权限';

        // 更新徽章
        this.elements.licenseTypeBadge.textContent = typeDisplay;
        this.elements.licenseTypeBadge.className = `license-type-badge ${license.licenseType.toLowerCase()}`;

        // 更新详情
        this.elements.licenseTypeValue.textContent = typeDisplay;
        this.elements.permissionLevelValue.textContent = permissionDisplay;

        // 更新到期时间
        if (license.licenseType === 'Professional') {
            this.elements.expiryDateValue.textContent = '永久有效';
        } else {
            const expiryDate = new Date(license.expiryDate);
            this.elements.expiryDateValue.textContent = expiryDate.toLocaleDateString('zh-CN');
        }
    }

    startAutoRedirectCountdown() {
        let countdown = 3;

        const updateCountdown = () => {
            this.elements.autoRedirect.innerHTML = `
                <span>${countdown}秒后自动进入主界面...</span>
                <button class="skip-countdown-btn" onclick="window.licenseController.proceedToMainApplication()">立即跳转</button>
            `;
            countdown--;

            if (countdown >= 0) {
                setTimeout(updateCountdown, 1000);
            } else {
                this.proceedToMainApplication();
            }
        };

        updateCountdown();
    }

    showActivationFailure(errorMessage) {
        this.showPageState('activationFailed');
        this.elements.errorMessage.textContent = errorMessage;
        this.elements.errorDetails.textContent = '请检查激活码是否正确，或联系技术支持获取帮助。';
    }

    updateLicenseStatusDisplay() {
        const license = this.currentLicense;
        if (!license) return;

        const typeDisplayNames = {
            'Trial': '试用版',
            'Standard': '标准版',
            'Professional': '专业版'
        };

        const permissionDisplayNames = {
            'Trial': '普通用户权限',
            'Standard': '售后服务用户权限',
            'Professional': '研发人员权限'
        };

        // 更新License类型徽章
        const typeDisplay = typeDisplayNames[license.licenseType] || license.licenseType;
        this.elements.statusLicenseTypeBadge.textContent = typeDisplay;
        this.elements.statusLicenseTypeBadge.className = `license-type-badge ${license.licenseType.toLowerCase()}`;

        // 更新状态信息
        this.elements.licenseStatusValue.textContent = license.isValid ? '有效' : '无效';
        this.elements.licenseStatusValue.className = `detail-value ${license.isValid ? 'valid' : 'expired'}`;

        // 更新剩余天数
        if (license.licenseType === 'Trial') {
            const expiryDate = new Date(license.expiryDate);
            const now = new Date();
            const remainingDays = Math.max(0, Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24)));
            this.elements.remainingDaysValue.textContent = `${remainingDays}天`;

            // 更新升级提示可见性
            this.elements.upgradePrompt.style.display = 'block';
        } else {
            this.elements.remainingDaysValue.textContent = '永久有效';
            this.elements.upgradePrompt.style.display = 'none';
        }

        // 更新权限级别
        this.elements.statusPermissionValue.textContent = permissionDisplayNames[license.licenseType] || '未知权限';

        // 更新功能限制
        this.updateFeatureRestrictions(license.licenseType);
    }

    updateFeatureRestrictions(licenseType) {
        const restrictions = {
            'Trial': [
                { icon: 'restricted', text: '设备连接数量限制: 最多2台' },
                { icon: 'restricted', text: '数据导出功能受限' },
                { icon: 'unavailable', text: '高级调试功能不可用' },
                { icon: 'unavailable', text: '自定义报告功能不可用' }
            ],
            'Standard': [
                { icon: 'available', text: '设备连接数量: 无限制' },
                { icon: 'available', text: '完整数据导出功能' },
                { icon: 'restricted', text: '部分高级调试功能' },
                { icon: 'unavailable', text: '自定义报告功能不可用' }
            ],
            'Professional': [
                { icon: 'available', text: '所有功能完全可用' },
                { icon: 'available', text: '无任何限制' }
            ]
        };

        const licenseRestrictions = restrictions[licenseType] || [];
        const restrictionList = this.elements.featureRestrictions.querySelector('.restriction-list');

        restrictionList.innerHTML = licenseRestrictions.map(restriction => `
            <div class="restriction-item">
                <span class="feature-restriction-icon ${restriction.icon}">
                    ${restriction.icon === 'available' ? '✓' : restriction.icon === 'restricted' ? '!' : '✕'}
                </span>
                <span>${restriction.text}</span>
            </div>
        `).join('');
    }

    // 导航和操作方法
    goBack() {
        if (confirm('确定要返回到启动页面吗？')) {
            window.location.href = 'startup-welcome.html';
        }
    }

    retryActivation() {
        this.activationCode = '';
        this.isActivationCodeValid = false;
        this.elements.activationCodeInput.value = '';
        this.elements.activationCodeInput.className = 'activation-code-input';
        this.elements.characterCounter.textContent = '0/20';
        this.elements.characterCounter.classList.remove('complete');
        this.elements.validationFeedback.textContent = '';
        this.elements.activationButton.disabled = true;

        this.showActivationInput();
    }

    async refreshLicenseStatus() {
        this.elements.refreshStatusButton.disabled = true;
        this.elements.refreshStatusButton.innerHTML = '<span class="button-icon">🔄</span> 刷新中...';

        try {
            // 模拟刷新过程
            await this.delay(1000);

            // 重新检查License
            const updatedLicense = await this.checkExistingLicense();
            if (updatedLicense) {
                this.currentLicense = updatedLicense;
                this.updateLicenseStatusDisplay();
            }

        } catch (error) {
            console.error('刷新License状态失败:', error);
        } finally {
            this.elements.refreshStatusButton.disabled = false;
            this.elements.refreshStatusButton.innerHTML = '<span class="button-icon">🔄</span> 刷新状态';
        }
    }

    proceedToMainApplication() {
        const license = this.currentLicense;
        if (!license) {
            alert('License信息不完整，无法进入主界面');
            return;
        }

        const permissionLevels = {
            'Trial': '普通用户权限',
            'Standard': '售后服务用户权限',
            'Professional': '研发人员权限'
        };

        const permissionLevel = permissionLevels[license.licenseType] || '未知权限';

        alert(`即将进入主应用程序界面\n\nLicense类型: ${license.licenseType}\n权限级别: ${permissionLevel}\n设备指纹: ${license.deviceFingerprint.substring(0, 16)}...`);

        // 在实际应用中，这里会导航到主应用程序
        // window.location.href = 'main-application.html';
    }

    // 信息展示方法
    showHelp() {
        this.showModal('帮助信息', `
            <h4>如何激活License？</h4>
            <ol>
                <li>输入您收到的20位License激活码</li>
                <li>激活码格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX</li>
                <li>点击"激活License"按钮</li>
                <li>等待激活完成</li>
            </ol>

            <h4>常见问题</h4>
            <ul>
                <li><strong>激活码无效：</strong>请检查激活码是否正确输入</li>
                <li><strong>设备不匹配：</strong>License只能在授权设备上使用</li>
                <li><strong>网络问题：</strong>激活过程需要网络连接</li>
            </ul>

            <h4>需要帮助？</h4>
            <p>如果遇到问题，请联系技术支持：</p>
            <p>电话：400-XXX-XXXX<br>邮箱：<EMAIL></p>
        `);
    }

    showSupport() {
        this.showModal('技术支持', `
            <h4>联系方式</h4>
            <div style="margin: 16px 0;">
                <p><strong>技术支持热线：</strong></p>
                <p>📞 400-XXX-XXXX</p>
                <p>🕒 工作时间：周一至周五 9:00-18:00</p>
            </div>

            <div style="margin: 16px 0;">
                <p><strong>邮件支持：</strong></p>
                <p>📧 <EMAIL></p>
                <p>📧 <EMAIL></p>
            </div>

            <div style="margin: 16px 0;">
                <p><strong>在线支持：</strong></p>
                <p>🌐 www.company.com/support</p>
                <p>💬 在线客服：工作时间内可用</p>
            </div>

            <div style="margin: 16px 0;">
                <p><strong>远程协助：</strong></p>
                <p>如需远程协助，请提前预约</p>
            </div>
        `);
    }

    contactSupport() {
        this.showSupport();
    }

    showLicenseTypesInfo() {
        this.showModal('License类型说明', `
            <div style="margin-bottom: 20px;">
                <h4 style="color: #FFB900;">🔶 试用版License</h4>
                <ul>
                    <li>免费使用30天</li>
                    <li>基础监控功能</li>
                    <li>最多连接2台设备</li>
                    <li>数据导出功能受限</li>
                </ul>
            </div>

            <div style="margin-bottom: 20px;">
                <h4 style="color: #0078D4;">🔷 标准版License</h4>
                <ul>
                    <li>完整的监控和诊断功能</li>
                    <li>无设备连接数量限制</li>
                    <li>完整的数据导出功能</li>
                    <li>售后服务工具</li>
                    <li>技术支持服务</li>
                </ul>
            </div>

            <div style="margin-bottom: 20px;">
                <h4 style="color: #107C10;">🔸 专业版License</h4>
                <ul>
                    <li>所有标准版功能</li>
                    <li>高级调试和测试工具</li>
                    <li>自定义报告功能</li>
                    <li>API接口访问</li>
                    <li>优先技术支持</li>
                    <li>定制化开发支持</li>
                </ul>
            </div>
        `);
    }

    showFeatureComparison() {
        this.showModal('功能对比', `
            <table style="width: 100%; border-collapse: collapse; margin: 16px 0;">
                <thead>
                    <tr style="background: #F3F2F1;">
                        <th style="padding: 8px; text-align: left; border: 1px solid #E1DFDD;">功能</th>
                        <th style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">试用版</th>
                        <th style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">标准版</th>
                        <th style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">专业版</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #E1DFDD;">基础监控</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">✅</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">✅</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">✅</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #E1DFDD;">设备连接数</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">最多2台</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">无限制</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">无限制</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #E1DFDD;">数据导出</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">受限</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">✅</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">✅</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #E1DFDD;">高级调试</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">❌</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">部分</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">✅</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #E1DFDD;">自定义报告</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">❌</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">❌</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">✅</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #E1DFDD;">技术支持</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">社区</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">标准</td>
                        <td style="padding: 8px; text-align: center; border: 1px solid #E1DFDD;">优先</td>
                    </tr>
                </tbody>
            </table>
        `);
    }

    showTechnicalSupport() {
        this.showSupport();
    }

    showUpgradeOptions() {
        this.showModal('升级选项', `
            <h4>升级到更高版本</h4>
            <p>解锁更多功能，提升工作效率</p>

            <div style="margin: 20px 0; padding: 16px; background: #F3F2F1; border-radius: 8px;">
                <h5>推荐：专业版License</h5>
                <ul>
                    <li>所有功能完全解锁</li>
                    <li>无任何使用限制</li>
                    <li>优先技术支持</li>
                    <li>定制化开发支持</li>
                </ul>
            </div>

            <h4>如何升级？</h4>
            <ol>
                <li>联系销售团队获取升级报价</li>
                <li>购买升级License</li>
                <li>使用新的激活码重新激活</li>
            </ol>

            <p><strong>联系销售：</strong></p>
            <p>📞 销售热线：400-XXX-XXXX</p>
            <p>📧 邮箱：<EMAIL></p>
        `);
    }

    // 模态对话框方法
    showModal(title, content) {
        this.elements.modalTitle.textContent = title;
        this.elements.modalBody.innerHTML = content;
        this.elements.modalOverlay.style.display = 'flex';

        setTimeout(() => {
            this.elements.modalOverlay.classList.add('show');
        }, 10);
    }

    hideModal() {
        this.elements.modalOverlay.classList.remove('show');

        setTimeout(() => {
            this.elements.modalOverlay.style.display = 'none';
        }, 300);
    }

    // 键盘事件处理
    handleKeyDown(event) {
        switch (event.key) {
            case 'F1':
                event.preventDefault();
                this.showHelp();
                break;
            case 'F5':
                if (this.currentState === 'activationFailed') {
                    event.preventDefault();
                    this.retryActivation();
                }
                break;
            case 'Escape':
                if (this.elements.modalOverlay.classList.contains('show')) {
                    this.hideModal();
                }
                break;
        }
    }

    // 页面可见性变化处理
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停某些操作
            console.log('页面已隐藏');
        } else {
            // 页面显示时恢复操作
            console.log('页面已显示');

            // 如果在License状态页面，刷新状态
            if (this.currentState === 'licenseStatus') {
                this.refreshLicenseStatus();
            }
        }
    }
}

// 页面加载完成后初始化控制器
document.addEventListener('DOMContentLoaded', () => {
    window.licenseController = new LicenseActivationController();
});

// 页面加载完成后初始化控制器
document.addEventListener('DOMContentLoaded', () => {
    new LicenseActivationController();
});
