/* 启动欢迎页面样式 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    overflow: hidden;
    user-select: none;
}

/* 主容器 */
.startup-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 顶部品牌区域 */
.brand-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px 40px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.brand-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.company-logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    width: 48px;
    height: 48px;
    margin-right: 16px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.brand-info {
    flex: 1;
    margin-left: 16px;
}

.app-title {
    font-size: 24px;
    font-weight: 600;
    color: #323130;
    margin-bottom: 4px;
}

.company-name {
    font-size: 14px;
    color: #605E5C;
    margin: 0;
}

.version-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 12px;
    color: #605E5C;
}

.version-label {
    margin-bottom: 2px;
}

.version-number {
    font-weight: 600;
    color: #0078D4;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    position: relative;
}

/* 应用图标 */
.app-icon-container {
    margin-bottom: 40px;
}

.app-icon {
    width: 128px;
    height: 128px;
    animation: logoFloat 3s ease-in-out infinite;
    filter: drop-shadow(0 8px 32px rgba(0, 120, 212, 0.3));
}

.hvac-icon {
    width: 100%;
    height: 100%;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

/* 状态区域 */
.status-section {
    text-align: center;
    max-width: 500px;
    width: 100%;
}

.status-message {
    font-size: 18px;
    font-weight: 500;
    color: #323130;
    margin-bottom: 24px;
    min-height: 24px;
    transition: all 0.3s ease;
}

.status-message.updating {
    opacity: 0.6;
    transform: translateY(-2px);
}

/* 进度条 */
.progress-container {
    margin-bottom: 24px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #E1E1E1;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #0078D4, #40E0FF);
    border-radius: 4px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.progress-text {
    text-align: center;
}

.progress-percentage {
    font-size: 14px;
    font-weight: 600;
    color: #0078D4;
    transition: all 0.3s ease;
}

.progress-percentage.updating {
    transform: scale(1.1);
}

/* 详细状态 */
.detailed-status {
    margin-bottom: 24px;
}

.status-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.status-icon {
    margin-right: 8px;
    font-size: 16px;
}

.status-text {
    font-size: 14px;
    color: #605E5C;
}

.status-item.completed {
    background: rgba(16, 124, 16, 0.1);
    border-left: 4px solid #107C10;
}

.status-item.completed .status-icon {
    color: #107C10;
}

.status-item.error {
    background: rgba(209, 52, 56, 0.1);
    border-left: 4px solid #D13438;
}

.status-item.error .status-icon {
    color: #D13438;
}

/* 设备信息 */
.device-info {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.info-label {
    font-size: 12px;
    color: #605E5C;
    margin-bottom: 4px;
}

.fingerprint-display {
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 14px;
    color: #323130;
    background: #F3F2F1;
    padding: 8px 12px;
    border-radius: 4px;
    letter-spacing: 1px;
    word-break: break-all;
}

/* 错误状态 */
.error-section {
    text-align: center;
    background: rgba(209, 52, 56, 0.1);
    border: 1px solid rgba(209, 52, 56, 0.3);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    animation: errorSlideIn 0.4s ease;
}

@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-message {
    font-size: 16px;
    font-weight: 500;
    color: #D13438;
    margin-bottom: 16px;
}

.error-toggle {
    cursor: pointer;
    font-size: 14px;
    color: #0078D4;
    margin-bottom: 8px;
    user-select: none;
    transition: color 0.2s ease;
}

.error-toggle:hover {
    color: #005A9E;
}

.error-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    font-family: 'Consolas', monospace;
    font-size: 12px;
    color: #605E5C;
    text-align: left;
}

.error-content.expanded {
    max-height: 200px;
    padding: 12px;
}

/* 成功状态 */
.success-section {
    text-align: center;
    background: rgba(16, 124, 16, 0.1);
    border: 1px solid rgba(16, 124, 16, 0.3);
    border-radius: 8px;
    padding: 24px;
    animation: successSlideIn 0.6s ease;
}

@keyframes successSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.success-icon {
    font-size: 48px;
    margin-bottom: 16px;
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.success-message {
    font-size: 18px;
    font-weight: 600;
    color: #107C10;
    margin-bottom: 16px;
}

.license-info {
    margin-bottom: 16px;
}

.license-type, .permission-level {
    font-size: 14px;
    color: #605E5C;
    margin-bottom: 4px;
}

.auto-redirect {
    font-size: 12px;
    color: #605E5C;
    font-style: italic;
    cursor: pointer;
    user-select: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.skip-countdown-btn {
    background: #0078D4;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 12px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 4px;
}

.skip-countdown-btn:hover {
    background: #005A9E;
    transform: scale(1.05);
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.button-secondary {
    background: transparent;
    color: #0078D4;
    border: 1px solid #E1DFDD;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    transform: scale(0);
    opacity: 0;
}

.button-secondary.visible {
    transform: scale(1);
    opacity: 1;
    animation: bounceIn 0.5s ease;
}

.button-secondary:hover {
    background: #F3F2F1;
    border-color: #0078D4;
    transform: scale(1.05);
}

.button-secondary:active {
    transform: scale(0.98);
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

.button-icon {
    font-size: 14px;
}

/* 底部信息 */
.footer-info {
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 16px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.copyright {
    font-size: 12px;
    color: #605E5C;
}

.footer-buttons {
    display: flex;
    gap: 16px;
}

.button-text {
    background: none;
    border: none;
    color: #0078D4;
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 2px;
    transition: all 0.2s ease;
}

.button-text:hover {
    background: rgba(0, 120, 212, 0.1);
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #E1E1E1;
    border-top: 4px solid #0078D4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1366px) {
    .app-icon {
        width: 96px;
        height: 96px;
    }
    
    .app-title {
        font-size: 20px;
    }
    
    .main-content {
        padding: 32px;
    }
}

@media (max-height: 768px) {
    .main-content {
        padding: 24px;
    }
    
    .app-icon {
        width: 80px;
        height: 80px;
    }
    
    .app-icon-container {
        margin-bottom: 24px;
    }
}

/* DPI缩放适配 */
@media (min-resolution: 144dpi) {
    .app-icon {
        transform: scale(1.25);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .progress-bar {
        border: 2px solid #000;
    }
    
    .status-item {
        border: 1px solid #000;
    }
}
