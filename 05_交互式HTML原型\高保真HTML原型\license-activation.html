<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商用HVAC空调监控调试软件 - License激活</title>
    <link rel="stylesheet" href="css/license-activation.css">
    <link rel="stylesheet" href="css/fluent-design.css">
</head>
<body>
    <div class="license-activation-container" id="activationContainer">
        <!-- 顶部导航区域 -->
        <header class="top-navigation">
            <div class="nav-content">
                <button class="nav-button back-button" id="backButton">
                    <span class="nav-icon">←</span>
                    <span>返回</span>
                </button>
                <div class="nav-title">
                    <h1 id="pageTitle">License激活</h1>
                    <p id="pageSubtitle">请输入您的License激活码</p>
                </div>
                <div class="nav-actions">
                    <button class="nav-button help-button" id="helpButton">
                        <span class="nav-icon">?</span>
                        <span>帮助</span>
                    </button>
                    <button class="nav-button support-button" id="supportButton">
                        <span class="nav-icon">📞</span>
                        <span>支持</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content" id="mainContent">
            <!-- 加载状态 -->
            <div class="page-state loading-state" id="loadingState">
                <div class="license-status-icon processing">
                    <div class="loading-spinner"></div>
                </div>
                <h2>正在加载...</h2>
                <p>请稍候</p>
            </div>

            <!-- 激活码输入状态 -->
            <div class="page-state activation-input-state" id="activationInputState">
                <div class="license-status-icon pending" id="statusIcon">
                    🔑
                </div>
                
                <div class="status-message" id="statusMessage">
                    欢迎使用商用HVAC空调监控调试软件
                </div>

                <div class="activation-form">
                    <div class="input-group">
                        <label for="activationCodeInput" class="input-label">License激活码</label>
                        <input type="text" 
                               id="activationCodeInput" 
                               class="activation-code-input"
                               placeholder="XXXXX-XXXXX-XXXXX-XXXXX-XXXXX"
                               maxlength="29"
                               autocomplete="off">
                        <div class="character-counter" id="characterCounter">0/20</div>
                        <div class="validation-feedback" id="validationFeedback"></div>
                    </div>

                    <div class="device-fingerprint-container" id="deviceFingerprintContainer">
                        <div class="device-fingerprint-label">设备指纹</div>
                        <div class="device-fingerprint-value" id="deviceFingerprintValue">
                            正在生成...
                        </div>
                        <button class="copy-fingerprint-btn" id="copyFingerprintBtn">
                            <span class="button-icon">📋</span>
                            复制
                        </button>
                        <div class="copy-tooltip" id="copyTooltip">已复制</div>
                    </div>

                    <button class="activation-button" id="activationButton" disabled>
                        <span class="button-text">激活License</span>
                    </button>
                </div>
            </div>

            <!-- 激活中状态 -->
            <div class="page-state activating-state" id="activatingState" style="display: none;">
                <div class="license-status-icon processing">
                    <div class="loading-spinner"></div>
                </div>
                
                <h2>正在激活License...</h2>
                <p id="activatingMessage">请稍候，正在验证激活码...</p>
                
                <div class="activation-progress-container active">
                    <div class="activation-progress-bar">
                        <div class="activation-progress-fill" id="activationProgressFill"></div>
                    </div>
                    <div class="progress-steps">
                        <span class="progress-step" id="step1">验证激活码</span>
                        <span class="progress-step" id="step2">生成License</span>
                        <span class="progress-step" id="step3">配置权限</span>
                        <span class="progress-step" id="step4">完成激活</span>
                    </div>
                </div>
            </div>

            <!-- 激活成功状态 -->
            <div class="page-state activation-success-state" id="activationSuccessState" style="display: none;">
                <div class="success-animation-container">
                    <div class="license-status-icon success">
                        ✅
                    </div>
                    
                    <h2 class="success-message">License激活成功！</h2>
                    
                    <div class="license-info-card">
                        <div class="license-type-badge" id="licenseTypeBadge">专业版</div>
                        <div class="license-details">
                            <div class="license-detail-item">
                                <span class="detail-label">License类型:</span>
                                <span class="detail-value" id="licenseTypeValue">专业版</span>
                            </div>
                            <div class="license-detail-item">
                                <span class="detail-label">权限级别:</span>
                                <span class="detail-value" id="permissionLevelValue">研发人员权限</span>
                            </div>
                            <div class="license-detail-item">
                                <span class="detail-label">有效期:</span>
                                <span class="detail-value" id="expiryDateValue">永久有效</span>
                            </div>
                        </div>
                    </div>

                    <button class="proceed-button" id="proceedButton">
                        <span class="button-text">进入主界面</span>
                    </button>
                    
                    <div class="auto-redirect" id="autoRedirect">
                        <span>3秒后自动跳转...</span>
                        <button class="skip-countdown-btn" id="skipCountdownBtn">立即跳转</button>
                    </div>
                </div>
            </div>

            <!-- 激活失败状态 -->
            <div class="page-state activation-failed-state" id="activationFailedState" style="display: none;">
                <div class="license-status-icon failed">
                    ❌
                </div>
                
                <h2>激活失败</h2>
                
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message" id="errorMessage">激活码无效或已被使用</div>
                    <div class="error-details" id="errorDetails">
                        请检查激活码是否正确，或联系技术支持获取帮助。
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="retry-button" id="retryButton">
                        <span class="button-icon">🔄</span>
                        重试激活
                    </button>
                    <button class="support-contact-button" id="contactSupportButton">
                        <span class="button-icon">📞</span>
                        联系支持
                    </button>
                </div>
            </div>

            <!-- License状态管理 -->
            <div class="page-state license-status-state" id="licenseStatusState" style="display: none;">
                <div class="license-status-icon success">
                    ✅
                </div>
                
                <h2>License状态管理</h2>
                
                <div class="license-status-card" id="licenseStatusCard">
                    <div class="license-type-badge trial" id="statusLicenseTypeBadge">试用版</div>
                    
                    <div class="license-status-details">
                        <div class="status-detail-item">
                            <span class="detail-label">License状态:</span>
                            <span class="detail-value valid" id="licenseStatusValue">有效</span>
                        </div>
                        <div class="status-detail-item">
                            <span class="detail-label">剩余天数:</span>
                            <span class="detail-value" id="remainingDaysValue">15天</span>
                        </div>
                        <div class="status-detail-item">
                            <span class="detail-label">权限级别:</span>
                            <span class="detail-value" id="statusPermissionValue">普通用户权限</span>
                        </div>
                    </div>

                    <div class="feature-restrictions" id="featureRestrictions">
                        <h4>功能限制</h4>
                        <div class="restriction-list">
                            <div class="restriction-item">
                                <span class="feature-restriction-icon restricted">!</span>
                                <span>设备连接数量限制: 最多2台</span>
                            </div>
                            <div class="restriction-item">
                                <span class="feature-restriction-icon restricted">!</span>
                                <span>数据导出功能受限</span>
                            </div>
                            <div class="restriction-item">
                                <span class="feature-restriction-icon unavailable">✕</span>
                                <span>高级调试功能不可用</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="upgrade-prompt" id="upgradePrompt">
                    <h3>升级到专业版</h3>
                    <p>解锁所有功能，享受完整的HVAC监控体验</p>
                    <button class="upgrade-button" id="upgradeButton">
                        <span class="button-icon">⬆️</span>
                        了解升级选项
                    </button>
                </div>

                <div class="status-actions">
                    <button class="refresh-button" id="refreshStatusButton">
                        <span class="button-icon">🔄</span>
                        刷新状态
                    </button>
                    <button class="proceed-button" id="statusProceedButton">
                        <span class="button-text">进入主界面</span>
                    </button>
                </div>
            </div>
        </main>

        <!-- 底部信息区域 -->
        <footer class="bottom-info">
            <div class="info-links">
                <button class="info-link" id="licenseTypesInfo">License类型说明</button>
                <button class="info-link" id="featureComparisonInfo">功能对比</button>
                <button class="info-link" id="technicalSupportInfo">联系技术支持</button>
            </div>
        </footer>
    </div>

    <!-- 模态对话框 -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <div class="modal-content" id="modalContent">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button class="modal-close" id="modalClose">✕</button>
            </div>
            <div class="modal-body" id="modalBody">
                内容
            </div>
            <div class="modal-footer">
                <button class="modal-button secondary" id="modalCancel">取消</button>
                <button class="modal-button primary" id="modalConfirm">确定</button>
            </div>
        </div>
    </div>

    <script src="js/license-activation.js"></script>
</body>
</html>
