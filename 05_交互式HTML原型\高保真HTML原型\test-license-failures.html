<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>License验证失败场景测试</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #0078D4;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #005A9E;
        }
        .iframe-container {
            margin-top: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .description {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #0078D4;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 License验证失败场景测试</h1>
        
        <div class="description">
            <h3>测试说明</h3>
            <p>此页面用于测试启动欢迎页面在不同License验证失败场景下的处理逻辑。点击下方按钮可以模拟不同的失败场景：</p>
            <ul>
                <li><strong>文件不存在：</strong> 模拟首次使用，License文件不存在的情况</li>
                <li><strong>License过期：</strong> 模拟License文件存在但已过期或损坏</li>
                <li><strong>设备不匹配：</strong> 模拟License文件存在但设备指纹不匹配</li>
                <li><strong>验证成功：</strong> 模拟License验证成功的正常流程</li>
            </ul>
            <p><strong>预期行为：</strong> 所有失败场景都应该平滑跳转到License激活界面，不显示错误弹窗。</p>
        </div>

        <div class="test-controls">
            <h3>选择测试场景：</h3>
            <button class="test-button" onclick="testScenario('file_not_found')">
                📄 License文件不存在
            </button>
            <button class="test-button" onclick="testScenario('expired')">
                ⏰ License已过期
            </button>
            <button class="test-button" onclick="testScenario('device_mismatch')">
                🔒 设备指纹不匹配
            </button>
            <button class="test-button" onclick="testScenario('success')">
                ✅ 验证成功（对照组）
            </button>
            <button class="test-button" onclick="testScenario('random')">
                🎲 随机场景（原始逻辑）
            </button>
        </div>

        <div class="iframe-container">
            <iframe id="testFrame" src="startup-welcome.html"></iframe>
        </div>

        <div class="test-results">
            <h3>测试结果记录：</h3>
            <div id="testLog" style="background: #f8f9fa; padding: 16px; border-radius: 4px; min-height: 100px; font-family: monospace; font-size: 12px;">
                等待测试开始...
            </div>
        </div>
    </div>

    <script>
        let testCounter = 0;
        
        function testScenario(scenario) {
            testCounter++;
            const timestamp = new Date().toLocaleTimeString();
            const iframe = document.getElementById('testFrame');
            const log = document.getElementById('testLog');
            
            // 记录测试开始
            log.innerHTML += `\n[${timestamp}] 测试 #${testCounter}: 开始测试场景 "${scenario}"`;
            
            // 重新加载iframe并传递测试参数
            const url = `startup-welcome.html?test=${scenario}&counter=${testCounter}`;
            iframe.src = url;
            
            // 监听iframe加载完成
            iframe.onload = function() {
                log.innerHTML += `\n[${new Date().toLocaleTimeString()}] 测试 #${testCounter}: 页面加载完成`;
                
                // 向iframe发送测试参数
                try {
                    iframe.contentWindow.postMessage({
                        type: 'TEST_SCENARIO',
                        scenario: scenario,
                        testId: testCounter
                    }, '*');
                    log.innerHTML += `\n[${new Date().toLocaleTimeString()}] 测试 #${testCounter}: 测试参数已发送`;
                } catch (e) {
                    log.innerHTML += `\n[${new Date().toLocaleTimeString()}] 测试 #${testCounter}: 发送测试参数失败 - ${e.message}`;
                }
            };
            
            // 滚动到底部
            setTimeout(() => {
                log.scrollTop = log.scrollHeight;
            }, 100);
        }
        
        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            
            if (event.data && event.data.type === 'TEST_RESULT') {
                const data = event.data;
                log.innerHTML += `\n[${timestamp}] 测试 #${data.testId}: ${data.message}`;
                
                if (data.result) {
                    log.innerHTML += `\n[${timestamp}] 测试 #${data.testId}: ✅ ${data.result}`;
                }
                
                if (data.error) {
                    log.innerHTML += `\n[${timestamp}] 测试 #${data.testId}: ❌ ${data.error}`;
                }
                
                // 滚动到底部
                log.scrollTop = log.scrollHeight;
            }
        });
        
        // 页面加载时自动开始随机测试
        window.onload = function() {
            setTimeout(() => {
                testScenario('random');
            }, 1000);
        };
    </script>
</body>
</html>
