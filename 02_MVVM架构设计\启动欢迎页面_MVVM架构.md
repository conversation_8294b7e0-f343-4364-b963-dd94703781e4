# 启动欢迎页面 - MVVM架构设计

## 1. 页面功能定义

### 1.1 页面职责
- 软件启动时的欢迎界面和品牌展示
- License文件自动检测和验证
- 设备指纹自动生成和匹配
- 系统初始化进度指示
- License验证结果处理和页面跳转

### 1.2 业务流程
```
软件启动 → 显示欢迎界面 → 生成设备指纹 → 检测License文件 →
├── License文件存在 → 验证License → 根据验证结果跳转：
│   ├── License有效 → 直接进入主界面（对应权限级别）
│   └── License无效/过期/设备不匹配 → 跳转到License激活页面（显示失败原因）
└── License文件不存在 → 直接跳转到License激活页面（首次激活）

所有License验证失败场景都会：
1. 显示具体失败原因
2. 3秒倒计时自动跳转
3. 允许用户手动跳过倒计时
4. 传递失败信息到License激活页面
```

## 2. ViewModel架构设计

### 2.1 StartupWelcomeViewModel
```csharp
public class StartupWelcomeViewModel : BaseViewModel
{
    #region 私有字段
    private readonly ILicenseValidationService _licenseService;
    private readonly IDeviceFingerprintService _fingerprintService;
    private readonly IPermissionMappingService _permissionService;
    private readonly INavigationService _navigationService;
    private CancellationTokenSource _cancellationTokenSource;
    #endregion

    #region 公共属性
    
    // 软件信息
    public string ApplicationName { get; set; } = "商用HVAC空调监控调试软件";
    public string ApplicationVersion { get; set; }
    public string CompanyName { get; set; } = "您的公司名称";
    public string CopyrightInfo { get; set; }
    
    // 启动状态
    public bool IsInitializing { get; set; } = true;
    public string InitializationStatus { get; set; } = "正在启动...";
    public double InitializationProgress { get; set; } = 0;
    public bool ShowProgressBar { get; set; } = true;
    
    // License验证状态
    public string LicenseValidationStatus { get; set; } = "正在验证License...";
    public bool IsLicenseValidating { get; set; } = false;
    public bool LicenseValidationCompleted { get; set; } = false;
    public bool LicenseValidationSuccess { get; set; } = false;
    public bool HasLicenseFile { get; set; } = false;

    // License失败信息
    public string LicenseFailureReason { get; set; }
    public string LicenseFailureMessage { get; set; }
    
    // 设备指纹状态
    public string DeviceFingerprint { get; set; }
    public bool DeviceFingerprintGenerated { get; set; } = false;
    public string DeviceFingerprintStatus { get; set; } = "正在生成设备指纹...";
    
    // 错误处理
    public bool HasError { get; set; } = false;
    public string ErrorMessage { get; set; }
    public bool ShowErrorDetails { get; set; } = false;
    public string ErrorDetails { get; set; }
    
    // 导航控制
    public bool CanProceed { get; set; } = false;
    public string NextPageTarget { get; set; }
    
    #endregion

    #region 命令
    
    public ICommand StartInitializationCommand { get; set; }
    public ICommand RetryInitializationCommand { get; set; }
    public ICommand ShowErrorDetailsCommand { get; set; }
    public ICommand ExitApplicationCommand { get; set; }
    public ICommand ProceedToNextPageCommand { get; set; }
    
    #endregion

    #region 构造函数
    
    public StartupWelcomeViewModel(
        ILicenseValidationService licenseService,
        IDeviceFingerprintService fingerprintService,
        IPermissionMappingService permissionService,
        INavigationService navigationService)
    {
        _licenseService = licenseService;
        _fingerprintService = fingerprintService;
        _permissionService = permissionService;
        _navigationService = navigationService;
        
        InitializeCommands();
        InitializeApplicationInfo();
    }
    
    #endregion

    #region 初始化方法
    
    private void InitializeCommands()
    {
        StartInitializationCommand = new AsyncRelayCommand(StartInitializationAsync);
        RetryInitializationCommand = new AsyncRelayCommand(RetryInitializationAsync);
        ShowErrorDetailsCommand = new RelayCommand(ShowErrorDetails);
        ExitApplicationCommand = new RelayCommand(ExitApplication);
        ProceedToNextPageCommand = new AsyncRelayCommand(ProceedToNextPageAsync);
    }
    
    private void InitializeApplicationInfo()
    {
        var assembly = Assembly.GetExecutingAssembly();
        ApplicationVersion = assembly.GetName().Version?.ToString() ?? "1.0.0.0";
        CopyrightInfo = $"© {DateTime.Now.Year} {CompanyName}. 保留所有权利。";
    }
    
    #endregion

    #region 核心业务方法
    
    /// <summary>
    /// 开始初始化流程
    /// </summary>
    private async Task StartInitializationAsync()
    {
        try
        {
            _cancellationTokenSource = new CancellationTokenSource();
            var token = _cancellationTokenSource.Token;
            
            IsInitializing = true;
            HasError = false;
            InitializationProgress = 0;
            
            // 步骤1: 生成设备指纹 (20%)
            await GenerateDeviceFingerprintAsync(token);
            
            // 步骤2: 检测License文件 (40%)
            await DetectLicenseFileAsync(token);
            
            // 步骤3: 验证License (70%)
            await ValidateLicenseAsync(token);
            
            // 步骤4: 确定导航目标 (90%)
            await DetermineNavigationTargetAsync(token);
            
            // 步骤5: 完成初始化 (100%)
            await CompleteInitializationAsync(token);
        }
        catch (OperationCanceledException)
        {
            InitializationStatus = "初始化已取消";
        }
        catch (Exception ex)
        {
            HandleInitializationError(ex);
        }
    }
    
    /// <summary>
    /// 生成设备指纹
    /// </summary>
    private async Task GenerateDeviceFingerprintAsync(CancellationToken token)
    {
        InitializationStatus = "正在生成设备指纹...";
        DeviceFingerprintStatus = "正在收集硬件信息...";
        
        await Task.Delay(500, token); // 模拟处理时间
        
        DeviceFingerprint = _fingerprintService.GenerateDeviceFingerprint();
        DeviceFingerprintGenerated = true;
        DeviceFingerprintStatus = "设备指纹生成完成";
        InitializationProgress = 20;
        
        OnPropertyChanged(nameof(DeviceFingerprint));
        OnPropertyChanged(nameof(DeviceFingerprintGenerated));
    }
    
    /// <summary>
    /// 检测License文件
    /// </summary>
    private async Task DetectLicenseFileAsync(CancellationToken token)
    {
        InitializationStatus = "正在检测License文件...";

        await Task.Delay(300, token);

        // 检测本地License文件是否存在
        var licenseExists = await _licenseService.CheckLicenseFileExistsAsync();
        HasLicenseFile = licenseExists;

        InitializationProgress = 40;

        if (!licenseExists)
        {
            InitializationStatus = "未检测到License文件，需要激活";
            NextPageTarget = "LicenseActivation";
            LicenseFailureReason = "file_not_found";
            LicenseFailureMessage = "License文件不存在，需要激活";
        }
        else
        {
            InitializationStatus = "License文件检测完成";
        }
    }
    
    /// <summary>
    /// 验证License
    /// </summary>
    private async Task ValidateLicenseAsync(CancellationToken token)
    {
        if (!HasLicenseFile)
        {
            InitializationProgress = 70;
            return; // 跳过验证，直接进入激活页面
        }

        InitializationStatus = "正在验证License...";
        LicenseValidationStatus = "正在验证License有效性...";
        IsLicenseValidating = true;

        await Task.Delay(800, token);

        try
        {
            var licenseInfo = await _licenseService.ValidateLicenseAsync(DeviceFingerprint);

            if (licenseInfo != null && licenseInfo.IsValid)
            {
                LicenseValidationSuccess = true;
                LicenseValidationStatus = $"License验证成功 - {licenseInfo.LicenseType}";

                // 映射License到权限级别
                CurrentLicenseType = licenseInfo.LicenseType;
                CurrentPermissionLevel = _permissionService.MapLicenseToPermission(licenseInfo.LicenseType);

                NextPageTarget = "MainApplication";
            }
            else
            {
                // License验证失败，设置失败原因并跳转到激活页面
                LicenseValidationSuccess = false;
                NextPageTarget = "LicenseActivation";

                // 根据具体失败原因设置不同的消息
                if (licenseInfo?.IsExpired == true)
                {
                    LicenseFailureReason = "expired";
                    LicenseFailureMessage = "License文件已过期或损坏";
                    LicenseValidationStatus = "License文件已过期，需要重新激活";
                }
                else if (licenseInfo?.DeviceFingerprintMismatch == true)
                {
                    LicenseFailureReason = "device_mismatch";
                    LicenseFailureMessage = "设备指纹不匹配，需要重新激活";
                    LicenseValidationStatus = "设备指纹不匹配，需要重新激活";
                }
                else
                {
                    LicenseFailureReason = "not_activated";
                    LicenseFailureMessage = "License验证失败，需要重新激活";
                    LicenseValidationStatus = "License验证失败，需要重新激活";
                }
            }
        }
        catch (Exception ex)
        {
            // 验证过程出错，也跳转到激活页面
            LicenseValidationSuccess = false;
            LicenseFailureReason = "validation_error";
            LicenseFailureMessage = $"License验证出错: {ex.Message}";
            LicenseValidationStatus = "License验证出错，需要重新激活";
            NextPageTarget = "LicenseActivation";
        }
        finally
        {
            IsLicenseValidating = false;
            LicenseValidationCompleted = true;
            InitializationProgress = 70;
        }
    }
    
    /// <summary>
    /// 确定导航目标
    /// </summary>
    private async Task DetermineNavigationTargetAsync(CancellationToken token)
    {
        InitializationStatus = "正在准备界面...";
        
        await Task.Delay(200, token);
        
        if (NextPageTarget == "MainApplication")
        {
            InitializationStatus = $"准备进入主界面 - {CurrentPermissionLevel} 权限";
        }
        else
        {
            InitializationStatus = "准备进入License激活界面";
        }
        
        InitializationProgress = 90;
    }
    
    /// <summary>
    /// 完成初始化
    /// </summary>
    private async Task CompleteInitializationAsync(CancellationToken token)
    {
        await Task.Delay(300, token);
        
        InitializationProgress = 100;
        InitializationStatus = "初始化完成";
        IsInitializing = false;
        CanProceed = true;
        
        // 自动跳转到下一页面（延迟1秒）
        await Task.Delay(1000, token);
        await ProceedToNextPageAsync();
    }
    
    #endregion

    #region 命令处理方法
    
    private async Task RetryInitializationAsync()
    {
        // 重置状态
        HasError = false;
        ErrorMessage = string.Empty;
        CanProceed = false;
        
        // 重新开始初始化
        await StartInitializationAsync();
    }
    
    private void ShowErrorDetails()
    {
        ShowErrorDetails = !ShowErrorDetails;
    }
    
    private void ExitApplication()
    {
        Application.Current.Shutdown();
    }
    
    private async Task ProceedToNextPageAsync()
    {
        if (!CanProceed) return;

        switch (NextPageTarget)
        {
            case "MainApplication":
                await _navigationService.NavigateToMainApplicationAsync(CurrentPermissionLevel);
                break;
            case "LicenseActivation":
                // 传递失败信息到License激活页面
                var activationContext = new LicenseActivationContext
                {
                    DeviceFingerprint = DeviceFingerprint,
                    FailureReason = LicenseFailureReason,
                    FailureMessage = LicenseFailureMessage,
                    IsFirstTimeActivation = LicenseFailureReason == "file_not_found"
                };
                await _navigationService.NavigateToLicenseActivationAsync(activationContext);
                break;
            default:
                await _navigationService.NavigateToLicenseActivationAsync();
                break;
        }
    }
    
    #endregion

    #region 错误处理
    
    private void HandleInitializationError(Exception ex)
    {
        HasError = true;
        ErrorMessage = "初始化过程中发生错误";
        ErrorDetails = ex.ToString();
        InitializationStatus = "初始化失败";
        IsInitializing = false;
        
        // 记录错误日志
        // Logger.LogError(ex, "Startup initialization failed");
    }
    
    #endregion

    #region 资源清理
    
    public override void Dispose()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        base.Dispose();
    }
    
    #endregion
}
```

## 3. 数据绑定策略

### 3.1 双向数据绑定
```xml
<!-- 初始化进度绑定 -->
<ProgressBar Value="{Binding InitializationProgress}" 
             Maximum="100" 
             Visibility="{Binding ShowProgressBar, Converter={StaticResource BoolToVisibilityConverter}}" />

<!-- 状态文本绑定 -->
<TextBlock Text="{Binding InitializationStatus}" 
           Style="{StaticResource StatusTextStyle}" />

<!-- 错误信息绑定 -->
<TextBlock Text="{Binding ErrorMessage}" 
           Visibility="{Binding HasError, Converter={StaticResource BoolToVisibilityConverter}}"
           Style="{StaticResource ErrorTextStyle}" />
```

### 3.2 命令绑定
```xml
<!-- 重试按钮 -->
<Button Command="{Binding RetryInitializationCommand}"
        Content="重试"
        Visibility="{Binding HasError, Converter={StaticResource BoolToVisibilityConverter}}" />

<!-- 退出按钮 -->
<Button Command="{Binding ExitApplicationCommand}"
        Content="退出" />
```

## 4. 页面生命周期

### 4.1 页面加载
```csharp
public async void OnPageLoaded()
{
    // 页面加载完成后自动开始初始化
    await StartInitializationCommand.ExecuteAsync(null);
}
```

### 4.2 页面卸载
```csharp
public void OnPageUnloaded()
{
    // 取消正在进行的初始化操作
    _cancellationTokenSource?.Cancel();
}
```

## 5. 下一步设计重点
1. **Fluent视觉设计**：设计启动欢迎页面的视觉样式和布局
2. **交互设计**：设计初始化过程的动画和用户反馈
3. **HTML原型制作**：制作像素级精确的交互原型
4. **XAML资源生成**：生成WPF XAML样式和模板
