# 启动欢迎页面 - 交互设计规范

## 1. 交互流程设计

### 1.1 主要交互流程
```
用户启动软件
    ↓
显示启动欢迎页面 (0.3s淡入动画)
    ↓
自动开始初始化流程
    ↓
步骤1: 生成设备指纹 (进度0-20%)
    ├── 显示"正在生成设备指纹..."
    ├── 显示硬件信息收集动画
    └── 完成后显示设备指纹前8位
    ↓
步骤2: 检测License文件 (进度20-40%)
    ├── 显示"正在检测License文件..."
    ├── 文件扫描动画效果
    └── 根据检测结果分支:
        ├── 文件存在 → 继续验证
        └── 文件不存在 → 标记跳转到激活页面
    ↓
步骤3: 验证License (进度40-70%)
    ├── 显示"正在验证License..."
    ├── 验证进度动画
    └── 根据验证结果分支:
        ├── 验证成功 → 显示License类型和权限级别
        └── 验证失败 → 标记跳转到激活页面
    ↓
步骤4: 准备界面 (进度70-90%)
    ├── 显示"正在准备界面..."
    └── 根据权限级别准备对应界面
    ↓
步骤5: 完成初始化 (进度90-100%)
    ├── 显示"初始化完成"
    ├── 成功动画效果
    └── 自动跳转到目标页面 (1秒延迟)
```

### 1.2 License验证失败处理流程
```
License验证失败（文件不存在/过期/设备不匹配）
    ↓
继续完成初始化流程（不显示错误）
    ↓
显示License激活重定向界面
    ├── 根据失败原因显示具体消息
    ├── 显示License状态和权限说明
    ├── 3秒倒计时自动跳转
    └── "立即跳转"按钮允许手动跳过倒计时
    ↓
跳转到License激活页面
    └── 传递失败原因、设备指纹等信息

### 1.3 系统异常处理流程（非License相关）
```
初始化过程中发生系统错误
    ↓
停止当前进度
    ↓
显示错误状态
    ├── 错误图标和消息
    ├── 显示"重试"按钮
    ├── 显示"查看详情"按钮
    └── 显示"退出"按钮
    ↓
用户选择操作:
    ├── 点击"重试" → 重新开始初始化
    ├── 点击"查看详情" → 展开/收起错误详情
    └── 点击"退出" → 关闭应用程序
```

## 2. 交互状态设计

### 2.1 页面状态定义
```typescript
enum PageState {
    Loading = "loading",           // 正在加载
    Initializing = "initializing", // 正在初始化
    Success = "success",           // 初始化成功
    Error = "error",               // 初始化失败
    Completed = "completed"        // 完成，准备跳转
}
```

### 2.2 初始化步骤状态
```typescript
enum InitializationStep {
    DeviceFingerprint = "device_fingerprint",  // 生成设备指纹
    LicenseDetection = "license_detection",    // 检测License文件
    LicenseValidation = "license_validation",  // 验证License
    UIPreparation = "ui_preparation",          // 准备界面
    Completion = "completion"                  // 完成初始化
}

interface StepStatus {
    step: InitializationStep;
    status: "pending" | "running" | "completed" | "failed";
    message: string;
    progress: number;
}
```

## 3. 用户交互元素设计

### 3.1 进度指示器交互
```css
/* 进度条交互状态 */
.progress-bar {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-bar.pulsing {
    animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 进度百分比显示 */
.progress-percentage {
    font-weight: 600;
    color: var(--primary-color);
    transition: all 0.3s ease;
}
```

### 3.2 状态文本交互
```css
/* 状态文本切换动画 */
.status-text {
    transition: all 0.3s ease;
    min-height: 20px;
}

.status-text.updating {
    opacity: 0.6;
    transform: translateY(-2px);
}

.status-text.updated {
    opacity: 1;
    transform: translateY(0);
}
```

### 3.3 按钮交互状态
```css
/* 重试按钮 */
.retry-button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    transform: scale(0);
    opacity: 0;
}

.retry-button.visible {
    transform: scale(1);
    opacity: 1;
    animation: bounceIn 0.5s ease;
}

.retry-button:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,120,212,0.3);
}

.retry-button:active {
    transform: scale(0.98);
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}
```

## 4. 动画和过渡效果

### 4.1 页面加载动画
```css
/* 整体页面淡入 */
.startup-page {
    animation: pageSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pageSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### 4.2 Logo和图标动画
```css
/* 软件Logo动画 */
.app-logo {
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* 加载图标旋转 */
.loading-icon {
    animation: iconSpin 1.5s linear infinite;
}

@keyframes iconSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
```

### 4.3 状态切换动画
```css
/* 状态指示器动画 */
.status-indicator {
    transition: all 0.4s ease;
}

.status-indicator.success {
    animation: successPulse 0.6s ease;
}

.status-indicator.error {
    animation: errorShake 0.5s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(16,124,16,0.4); }
    100% { transform: scale(1); }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
```

### 4.4 进度动画
```css
/* 进度条填充动画 */
.progress-fill {
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255,255,255,0.4), 
        transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
```

## 5. 响应式交互设计

### 5.1 触控设备适配
```css
/* 触控设备按钮尺寸 */
@media (pointer: coarse) {
    .retry-button, .exit-button {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 24px;
    }
}
```

### 5.2 键盘导航支持
```css
/* 键盘焦点指示 */
.focusable:focus {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(0,120,212,0.2);
}

/* Tab顺序优化 */
.startup-container {
    /* 确保按钮按逻辑顺序获得焦点 */
}
```

## 6. 用户反馈机制

### 6.1 视觉反馈
```css
/* 悬停反馈 */
.interactive-element:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
}

/* 点击反馈 */
.interactive-element:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0,0,0,0.15);
}
```

### 6.2 状态反馈
```typescript
// 状态变化反馈
interface StatusFeedback {
    type: "info" | "success" | "warning" | "error";
    message: string;
    duration?: number;
    showIcon?: boolean;
    allowDismiss?: boolean;
}
```

### 6.3 进度反馈
```css
/* 进度数字动画 */
.progress-number {
    transition: all 0.3s ease;
}

.progress-number.updating {
    transform: scale(1.1);
    color: var(--primary-color);
}
```

## 7. 错误状态交互

### 7.1 错误显示动画
```css
.error-container {
    opacity: 0;
    transform: translateY(10px);
    animation: errorSlideIn 0.4s ease forwards;
}

@keyframes errorSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### 7.2 错误详情展开
```css
.error-details {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.error-details.expanded {
    max-height: 200px;
}

.error-toggle {
    cursor: pointer;
    user-select: none;
}

.error-toggle::after {
    content: '▼';
    transition: transform 0.3s ease;
}

.error-toggle.expanded::after {
    transform: rotate(180deg);
}
```

## 8. 性能优化交互

### 8.1 动画性能优化
```css
/* 使用GPU加速 */
.animated-element {
    will-change: transform, opacity;
    transform: translateZ(0);
}

/* 减少重绘 */
.progress-bar {
    contain: layout style paint;
}
```

### 8.2 交互响应优化
```typescript
// 防抖处理
const debouncedRetry = debounce(() => {
    retryInitialization();
}, 300);

// 节流处理
const throttledProgress = throttle((progress: number) => {
    updateProgressBar(progress);
}, 100);
```

## 9. 无障碍交互设计

### 9.1 屏幕阅读器支持
```html
<!-- ARIA标签 -->
<div role="progressbar" 
     aria-valuenow="50" 
     aria-valuemin="0" 
     aria-valuemax="100"
     aria-label="初始化进度">
</div>

<div role="status" aria-live="polite">
    正在验证License...
</div>
```

### 9.2 键盘快捷键
```typescript
// 键盘事件处理
document.addEventListener('keydown', (e) => {
    switch(e.key) {
        case 'F5':
            e.preventDefault();
            retryInitialization();
            break;
        case 'Escape':
            if (hasError) {
                exitApplication();
            }
            break;
    }
});
```

## 10. 交互测试要点

### 10.1 功能测试
- [ ] 初始化流程正常执行
- [ ] 进度条平滑更新
- [ ] 状态文本及时切换
- [ ] 错误状态正确显示
- [ ] 重试功能正常工作
- [ ] 自动跳转正确执行

### 10.2 用户体验测试
- [ ] 动画流畅自然
- [ ] 反馈及时明确
- [ ] 错误信息清晰
- [ ] 操作响应迅速
- [ ] 视觉层次清晰
- [ ] 无障碍访问正常

### 10.3 性能测试
- [ ] 动画帧率稳定
- [ ] 内存使用合理
- [ ] CPU占用正常
- [ ] 响应时间 < 100ms

## 11. 下一步设计重点
1. **HTML原型制作**：制作像素级精确的交互原型
2. **XAML资源生成**：生成WPF XAML样式和模板
3. **用户测试验证**：验证交互设计的可用性
