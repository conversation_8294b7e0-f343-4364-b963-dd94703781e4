# License激活/状态页面 - MVVM架构设计

## 1. 页面功能定义

### 1.1 页面职责
- License激活码输入和验证
- License状态显示和管理
- 试用版功能限制说明和升级引导
- 设备指纹显示和管理
- License类型对应权限级别展示

### 1.2 业务流程
```
从启动欢迎页面跳转 → 接收失败原因和设备指纹 → 
根据场景显示不同界面：
├── 首次激活 → 显示激活码输入界面
├── License过期 → 显示重新激活界面 + 过期提示
├── 设备不匹配 → 显示重新激活界面 + 设备提示
└── 已激活状态 → 显示License状态管理界面

激活流程：
输入激活码 → 验证激活码 → 生成License文件 → 
根据License类型跳转：
├── 试用版 → 显示功能限制 + 升级提示 → 进入主界面
├── 标准版 → 显示售后功能说明 → 进入主界面
└── 专业版 → 显示完整功能说明 → 进入主界面
```

### 1.3 页面状态管理
```
页面状态枚举：
- Loading: 页面加载中
- ActivationInput: 激活码输入状态
- Activating: 正在激活License
- ActivationSuccess: 激活成功
- ActivationFailed: 激活失败
- LicenseStatus: License状态管理
- UpgradePrompt: 升级提示状态
```

## 2. ViewModel架构设计

### 2.1 LicenseActivationViewModel
```csharp
public class LicenseActivationViewModel : BaseViewModel
{
    #region 私有字段
    private readonly ILicenseValidationService _licenseService;
    private readonly IDeviceFingerprintService _fingerprintService;
    private readonly IPermissionMappingService _permissionService;
    private readonly INavigationService _navigationService;
    private readonly IEncryptionService _encryptionService;
    private LicenseActivationContext _activationContext;
    #endregion

    #region 公共属性
    
    // 页面状态
    public LicenseActivationPageState CurrentPageState { get; set; } = LicenseActivationPageState.Loading;
    public bool IsLoading { get; set; } = true;
    public bool IsActivating { get; set; } = false;
    public string PageTitle { get; set; } = "License激活";
    public string PageSubtitle { get; set; } = "请输入您的License激活码";
    
    // 激活上下文信息
    public string DeviceFingerprint { get; set; }
    public string FailureReason { get; set; }
    public string FailureMessage { get; set; }
    public bool IsFirstTimeActivation { get; set; } = true;
    public string PreviousLicenseType { get; set; }
    
    // 激活码输入
    public string ActivationCode { get; set; } = "";
    public bool IsActivationCodeValid { get; set; } = false;
    public string ActivationCodeError { get; set; } = "";
    public int ActivationCodeLength { get; set; } = 25; // XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
    
    // License信息
    public LicenseInfo CurrentLicense { get; set; }
    public string LicenseType { get; set; }
    public string LicenseStatus { get; set; }
    public DateTime? LicenseExpiryDate { get; set; }
    public string PermissionLevel { get; set; }
    public bool IsTrialLicense { get; set; } = false;
    public int TrialDaysRemaining { get; set; } = 0;
    
    // 功能限制和升级
    public ObservableCollection<FeatureRestriction> FeatureRestrictions { get; set; }
    public ObservableCollection<UpgradeOption> UpgradeOptions { get; set; }
    public bool ShowUpgradePrompt { get; set; } = false;
    public string UpgradeMessage { get; set; }
    
    // 状态消息
    public string StatusMessage { get; set; } = "";
    public bool HasError { get; set; } = false;
    public string ErrorMessage { get; set; } = "";
    public bool ShowErrorDetails { get; set; } = false;
    public string ErrorDetails { get; set; } = "";
    
    // UI控制
    public bool ShowActivationInput { get; set; } = true;
    public bool ShowLicenseStatus { get; set; } = false;
    public bool ShowSuccessMessage { get; set; } = false;
    public bool CanProceedToMain { get; set; } = false;
    public bool CanRetry { get; set; } = false;
    
    #endregion

    #region 命令
    
    public ICommand ValidateActivationCodeCommand { get; set; }
    public ICommand ActivateLicenseCommand { get; set; }
    public ICommand RetryActivationCommand { get; set; }
    public ICommand ProceedToMainCommand { get; set; }
    public ICommand ShowUpgradeOptionsCommand { get; set; }
    public ICommand ContactSupportCommand { get; set; }
    public ICommand CopyDeviceFingerprintCommand { get; set; }
    public ICommand RefreshLicenseStatusCommand { get; set; }
    public ICommand BackToStartupCommand { get; set; }
    
    #endregion

    #region 构造函数
    
    public LicenseActivationViewModel(
        ILicenseValidationService licenseService,
        IDeviceFingerprintService fingerprintService,
        IPermissionMappingService permissionService,
        INavigationService navigationService,
        IEncryptionService encryptionService)
    {
        _licenseService = licenseService;
        _fingerprintService = fingerprintService;
        _permissionService = permissionService;
        _navigationService = navigationService;
        _encryptionService = encryptionService;
        
        InitializeCommands();
        InitializeCollections();
    }
    
    #endregion

    #region 初始化方法
    
    private void InitializeCommands()
    {
        ValidateActivationCodeCommand = new RelayCommand<string>(ValidateActivationCode);
        ActivateLicenseCommand = new AsyncRelayCommand(ActivateLicenseAsync);
        RetryActivationCommand = new AsyncRelayCommand(RetryActivationAsync);
        ProceedToMainCommand = new AsyncRelayCommand(ProceedToMainAsync);
        ShowUpgradeOptionsCommand = new RelayCommand(ShowUpgradeOptions);
        ContactSupportCommand = new RelayCommand(ContactSupport);
        CopyDeviceFingerprintCommand = new RelayCommand(CopyDeviceFingerprint);
        RefreshLicenseStatusCommand = new AsyncRelayCommand(RefreshLicenseStatusAsync);
        BackToStartupCommand = new AsyncRelayCommand(BackToStartupAsync);
    }
    
    private void InitializeCollections()
    {
        FeatureRestrictions = new ObservableCollection<FeatureRestriction>();
        UpgradeOptions = new ObservableCollection<UpgradeOption>();
    }
    
    /// <summary>
    /// 初始化页面，接收来自启动页面的上下文
    /// </summary>
    public async Task InitializeAsync(LicenseActivationContext context)
    {
        try
        {
            IsLoading = true;
            _activationContext = context ?? new LicenseActivationContext();
            
            // 设置基本信息
            DeviceFingerprint = _activationContext.DeviceFingerprint ?? 
                               _fingerprintService.GenerateDeviceFingerprint();
            FailureReason = _activationContext.FailureReason;
            FailureMessage = _activationContext.FailureMessage;
            IsFirstTimeActivation = _activationContext.IsFirstTimeActivation;
            PreviousLicenseType = _activationContext.PreviousLicenseType;
            
            // 根据失败原因设置页面状态
            await SetupPageByFailureReasonAsync();
            
            // 检查是否已有有效License
            await CheckExistingLicenseAsync();
            
        }
        catch (Exception ex)
        {
            HandleError("页面初始化失败", ex);
        }
        finally
        {
            IsLoading = false;
        }
    }
    
    #endregion

    #region 核心业务方法
    
    /// <summary>
    /// 根据失败原因设置页面状态
    /// </summary>
    private async Task SetupPageByFailureReasonAsync()
    {
        switch (FailureReason)
        {
            case "file_not_found":
                PageTitle = "License激活";
                PageSubtitle = "首次使用，请输入您的License激活码";
                StatusMessage = "欢迎使用商用HVAC空调监控调试软件";
                IsFirstTimeActivation = true;
                break;
                
            case "expired":
                PageTitle = "License重新激活";
                PageSubtitle = "您的License已过期，请重新激活";
                StatusMessage = "License文件已过期或损坏，需要重新激活";
                IsFirstTimeActivation = false;
                break;
                
            case "device_mismatch":
                PageTitle = "License重新激活";
                PageSubtitle = "设备验证失败，请在当前设备上重新激活";
                StatusMessage = "License与当前设备不匹配，需要重新激活";
                IsFirstTimeActivation = false;
                break;
                
            default:
                PageTitle = "License激活";
                PageSubtitle = "请输入您的License激活码";
                StatusMessage = "请激活License以使用软件功能";
                break;
        }
        
        CurrentPageState = LicenseActivationPageState.ActivationInput;
        ShowActivationInput = true;
        ShowLicenseStatus = false;
    }
    
    /// <summary>
    /// 检查是否已有有效License
    /// </summary>
    private async Task CheckExistingLicenseAsync()
    {
        try
        {
            var existingLicense = await _licenseService.GetCurrentLicenseAsync();
            
            if (existingLicense != null && existingLicense.IsValid && 
                !existingLicense.IsExpired && 
                existingLicense.DeviceFingerprint == DeviceFingerprint)
            {
                // 已有有效License，显示状态管理界面
                CurrentLicense = existingLicense;
                await SetupLicenseStatusDisplayAsync();
            }
        }
        catch (Exception ex)
        {
            // 忽略检查错误，继续显示激活界面
            System.Diagnostics.Debug.WriteLine($"检查现有License失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 验证激活码格式
    /// </summary>
    private void ValidateActivationCode(string code)
    {
        ActivationCode = code?.ToUpper() ?? "";
        ActivationCodeError = "";
        
        // 基本格式验证
        if (string.IsNullOrWhiteSpace(ActivationCode))
        {
            IsActivationCodeValid = false;
            return;
        }
        
        // 移除分隔符进行验证
        var cleanCode = ActivationCode.Replace("-", "").Replace(" ", "");
        
        if (cleanCode.Length != 20)
        {
            IsActivationCodeValid = false;
            ActivationCodeError = "激活码应为20位字符";
            return;
        }
        
        // 检查字符是否都是字母数字
        if (!cleanCode.All(c => char.IsLetterOrDigit(c)))
        {
            IsActivationCodeValid = false;
            ActivationCodeError = "激活码只能包含字母和数字";
            return;
        }
        
        IsActivationCodeValid = true;
        
        // 自动格式化激活码
        if (cleanCode.Length == 20)
        {
            ActivationCode = $"{cleanCode.Substring(0, 4)}-{cleanCode.Substring(4, 4)}-" +
                           $"{cleanCode.Substring(8, 4)}-{cleanCode.Substring(12, 4)}-" +
                           $"{cleanCode.Substring(16, 4)}";
        }
    }
    
    /// <summary>
    /// 激活License
    /// </summary>
    private async Task ActivateLicenseAsync()
    {
        if (!IsActivationCodeValid)
        {
            ActivationCodeError = "请输入有效的激活码";
            return;
        }
        
        try
        {
            IsActivating = true;
            CurrentPageState = LicenseActivationPageState.Activating;
            StatusMessage = "正在激活License...";
            HasError = false;
            
            // 调用License激活服务
            var activationResult = await _licenseService.ActivateLicenseAsync(
                ActivationCode, DeviceFingerprint);
            
            if (activationResult.IsSuccess)
            {
                CurrentLicense = activationResult.LicenseInfo;
                await HandleActivationSuccessAsync();
            }
            else
            {
                await HandleActivationFailureAsync(activationResult.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            await HandleActivationFailureAsync($"激活过程中发生错误: {ex.Message}");
        }
        finally
        {
            IsActivating = false;
        }
    }
    
    /// <summary>
    /// 处理激活成功
    /// </summary>
    private async Task HandleActivationSuccessAsync()
    {
        CurrentPageState = LicenseActivationPageState.ActivationSuccess;
        StatusMessage = "License激活成功！";
        ShowSuccessMessage = true;
        ShowActivationInput = false;
        
        // 设置License信息
        await SetupLicenseStatusDisplayAsync();
        
        // 根据License类型显示相应信息
        await SetupLicenseTypeInfoAsync();
        
        // 延迟3秒后允许进入主界面
        await Task.Delay(3000);
        CanProceedToMain = true;
        
        // 自动跳转倒计时
        await StartAutoRedirectCountdownAsync();
    }
    
    /// <summary>
    /// 设置License状态显示
    /// </summary>
    private async Task SetupLicenseStatusDisplayAsync()
    {
        if (CurrentLicense == null) return;
        
        LicenseType = CurrentLicense.LicenseType.ToString();
        LicenseStatus = CurrentLicense.IsValid ? "有效" : "无效";
        LicenseExpiryDate = CurrentLicense.ExpiryDate;
        PermissionLevel = _permissionService.MapLicenseToPermission(CurrentLicense.LicenseType).ToString();
        
        IsTrialLicense = CurrentLicense.LicenseType == LicenseType.Trial;
        if (IsTrialLicense && CurrentLicense.ExpiryDate.HasValue)
        {
            TrialDaysRemaining = Math.Max(0, 
                (int)(CurrentLicense.ExpiryDate.Value - DateTime.Now).TotalDays);
        }
        
        ShowLicenseStatus = true;
        
        // 加载功能限制信息
        await LoadFeatureRestrictionsAsync();
        
        // 检查是否需要显示升级提示
        CheckUpgradePrompt();
    }
    
    #endregion

    #region 命令处理方法
    
    private async Task RetryActivationAsync()
    {
        HasError = false;
        ErrorMessage = "";
        ActivationCode = "";
        IsActivationCodeValid = false;
        CurrentPageState = LicenseActivationPageState.ActivationInput;
        ShowActivationInput = true;
        ShowSuccessMessage = false;
        CanProceedToMain = false;
    }
    
    private async Task ProceedToMainAsync()
    {
        if (!CanProceedToMain || CurrentLicense == null) return;
        
        var permissionLevel = _permissionService.MapLicenseToPermission(CurrentLicense.LicenseType);
        await _navigationService.NavigateToMainApplicationAsync(permissionLevel);
    }
    
    private void ShowUpgradeOptions()
    {
        ShowUpgradePrompt = true;
        CurrentPageState = LicenseActivationPageState.UpgradePrompt;
    }
    
    private void ContactSupport()
    {
        // 打开技术支持联系方式
        var supportInfo = "技术支持联系方式:\n" +
                         "电话: 400-XXX-XXXX\n" +
                         "邮箱: <EMAIL>\n" +
                         "在线客服: www.company.com/support";
        
        // 在实际应用中，这里会打开支持页面或复制联系信息
        System.Windows.MessageBox.Show(supportInfo, "技术支持", 
            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }
    
    private void CopyDeviceFingerprint()
    {
        try
        {
            System.Windows.Clipboard.SetText(DeviceFingerprint);
            StatusMessage = "设备指纹已复制到剪贴板";
        }
        catch (Exception ex)
        {
            StatusMessage = $"复制失败: {ex.Message}";
        }
    }
    
    #endregion
}

public enum LicenseActivationPageState
{
    Loading,
    ActivationInput,
    Activating,
    ActivationSuccess,
    ActivationFailed,
    LicenseStatus,
    UpgradePrompt
}

public class FeatureRestriction
{
    public string FeatureName { get; set; }
    public string Description { get; set; }
    public bool IsRestricted { get; set; }
    public string UpgradeRequirement { get; set; }
}

public class UpgradeOption
{
    public string LicenseType { get; set; }
    public string Description { get; set; }
    public List<string> Features { get; set; }
    public string ContactInfo { get; set; }
}
```

## 3. 数据绑定策略

### 3.1 双向数据绑定
```xml
<!-- 激活码输入绑定 -->
<TextBox Text="{Binding ActivationCode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
         IsEnabled="{Binding IsActivating, Converter={StaticResource InverseBooleanConverter}}" />

<!-- License状态绑定 -->
<TextBlock Text="{Binding LicenseType}" 
           Visibility="{Binding ShowLicenseStatus, Converter={StaticResource BoolToVisibilityConverter}}" />

<!-- 错误信息绑定 -->
<TextBlock Text="{Binding ErrorMessage}" 
           Visibility="{Binding HasError, Converter={StaticResource BoolToVisibilityConverter}}"
           Style="{StaticResource ErrorTextStyle}" />
```

### 3.2 命令绑定
```xml
<!-- 激活按钮 -->
<Button Command="{Binding ActivateLicenseCommand}"
        Content="激活License"
        IsEnabled="{Binding IsActivationCodeValid}" />

<!-- 重试按钮 -->
<Button Command="{Binding RetryActivationCommand}"
        Content="重试"
        Visibility="{Binding HasError, Converter={StaticResource BoolToVisibilityConverter}}" />

<!-- 进入主界面按钮 -->
<Button Command="{Binding ProceedToMainCommand}"
        Content="进入主界面"
        IsEnabled="{Binding CanProceedToMain}" />
```

## 4. 页面生命周期

### 4.1 页面加载
```csharp
public async void OnPageLoaded(LicenseActivationContext context)
{
    await ViewModel.InitializeAsync(context);
}
```

### 4.2 页面卸载
```csharp
public void OnPageUnloaded()
{
    ViewModel?.Dispose();
}
```

## 5. 下一步设计重点
1. **Fluent视觉设计**：设计License激活页面的视觉样式和布局
2. **交互设计**：设计激活流程的交互和用户反馈
3. **HTML原型制作**：制作像素级精确的交互原型
4. **XAML资源生成**：生成WPF XAML样式和模板
