/* License激活/状态页面样式 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    user-select: none;
}

/* 主容器 */
.license-activation-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
}

/* 顶部导航区域 */
.top-navigation {
    background: rgba(255, 255, 255, 0.98);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 16px 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-button {
    background: transparent;
    border: 1px solid #E1DFDD;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #605E5C;
}

.nav-button:hover {
    background: #F3F2F1;
    border-color: #0078D4;
    color: #0078D4;
}

.nav-icon {
    font-size: 16px;
}

.nav-title {
    text-align: center;
    flex: 1;
    margin: 0 24px;
}

.nav-title h1 {
    font-size: 20px;
    font-weight: 600;
    color: #323130;
    margin-bottom: 4px;
}

.nav-title p {
    font-size: 14px;
    color: #605E5C;
}

.nav-actions {
    display: flex;
    gap: 8px;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 40px 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* 页面状态容器 */
.page-state {
    text-align: center;
    max-width: 500px;
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-state.active {
    opacity: 1;
    transform: translateY(0);
}

/* License状态图标 */
.license-status-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    position: relative;
    transition: all 0.3s ease;
}

.license-status-icon.pending {
    background: rgba(0, 120, 212, 0.1);
    color: #0078D4;
    border: 3px solid rgba(0, 120, 212, 0.3);
}

.license-status-icon.processing {
    background: rgba(64, 224, 255, 0.1);
    color: #40E0FF;
    border: 3px solid rgba(64, 224, 255, 0.3);
    animation: processingPulse 2s ease-in-out infinite;
}

.license-status-icon.success {
    background: rgba(16, 124, 16, 0.1);
    color: #107C10;
    border: 3px solid rgba(16, 124, 16, 0.3);
    animation: successPulse 0.6s ease;
}

.license-status-icon.failed {
    background: rgba(209, 52, 56, 0.1);
    color: #D13438;
    border: 3px solid rgba(209, 52, 56, 0.3);
    animation: failedShake 0.5s ease;
}

@keyframes processingPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes failedShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(64, 224, 255, 0.3);
    border-top: 4px solid #40E0FF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态消息 */
.status-message {
    font-size: 16px;
    color: #605E5C;
    margin-bottom: 32px;
    line-height: 1.5;
}

/* 激活表单 */
.activation-form {
    width: 100%;
}

.input-group {
    margin-bottom: 24px;
    position: relative;
}

.input-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #323130;
    margin-bottom: 8px;
}

/* 激活码输入框 */
.activation-code-input {
    width: 100%;
    height: 56px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 2px;
    border: 2px solid #E1DFDD;
    border-radius: 8px;
    background: #FFFFFF;
    color: #323130;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.activation-code-input:focus {
    border-color: #0078D4;
    box-shadow: 0 0 0 4px rgba(0, 120, 212, 0.2);
    outline: none;
    transform: scale(1.02);
}

.activation-code-input.valid {
    border-color: #107C10;
    background: rgba(16, 124, 16, 0.05);
}

.activation-code-input.invalid {
    border-color: #D13438;
    background: rgba(209, 52, 56, 0.05);
    animation: inputShake 0.5s ease;
}

@keyframes inputShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 字符计数器 */
.character-counter {
    position: absolute;
    bottom: -20px;
    right: 0;
    font-size: 12px;
    color: #605E5C;
    transition: color 0.3s ease;
}

.character-counter.complete {
    color: #107C10;
    font-weight: 600;
}

/* 验证反馈 */
.validation-feedback {
    font-size: 12px;
    margin-top: 4px;
    transition: all 0.3s ease;
    min-height: 16px;
}

.validation-feedback.error {
    color: #D13438;
    animation: validationError 0.3s ease;
}

.validation-feedback.success {
    color: #107C10;
    animation: validationSuccess 0.3s ease;
}

@keyframes validationError {
    0% { opacity: 0; transform: translateY(-5px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes validationSuccess {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
}

/* 设备指纹容器 */
.device-fingerprint-container {
    background: #F3F2F1;
    border: 1px solid #E1DFDD;
    border-radius: 8px;
    padding: 16px;
    margin: 24px 0;
    position: relative;
    transition: all 0.3s ease;
}

.device-fingerprint-container:hover {
    background: #EDEBE9;
    transform: translateY(-1px);
}

.device-fingerprint-label {
    font-size: 12px;
    color: #605E5C;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.device-fingerprint-value {
    font-family: 'Consolas', monospace;
    font-size: 14px;
    color: #323130;
    word-break: break-all;
    line-height: 1.4;
    margin-bottom: 12px;
    padding: 8px;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #E1DFDD;
}

.copy-fingerprint-btn {
    background: transparent;
    border: 1px solid #E1DFDD;
    color: #0078D4;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.copy-fingerprint-btn:hover {
    background: #F3F2F1;
    border-color: #0078D4;
    transform: scale(1.05);
}

.copy-fingerprint-btn:active {
    transform: scale(0.98);
}

.copy-fingerprint-btn.copied {
    background: #107C10;
    color: white;
    border-color: #107C10;
    animation: copySuccess 2s ease;
}

@keyframes copySuccess {
    0% { background: #107C10; }
    50% { background: #107C10; }
    100% { background: transparent; }
}

.copy-tooltip {
    position: absolute;
    top: -30px;
    right: 0;
    background: #323130;
    color: #FFFFFF;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transform: translateY(5px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.copy-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

/* 激活按钮 */
.activation-button {
    background: linear-gradient(135deg, #0078D4, #40E0FF);
    color: #FFFFFF;
    border: none;
    border-radius: 8px;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 160px;
    width: 100%;
}

.activation-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.activation-button:hover:not(:disabled)::before {
    left: 100%;
}

.activation-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 120, 212, 0.4);
}

.activation-button:active:not(:disabled) {
    transform: translateY(0);
}

.activation-button:disabled {
    background: #A19F9D;
    color: #FFFFFF;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.activation-button.loading {
    pointer-events: none;
    color: transparent;
}

.activation-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #FFFFFF;
    border-radius: 50%;
    animation: buttonSpin 1s linear infinite;
}

@keyframes buttonSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 激活进度 */
.activation-progress-container {
    width: 100%;
    margin: 20px 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activation-progress-container.active {
    opacity: 1;
}

.activation-progress-bar {
    height: 4px;
    background: #E1E1E1;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
    margin-bottom: 12px;
}

.activation-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0078D4, #40E0FF);
    border-radius: 2px;
    width: 0%;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.activation-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-steps {
    display: flex;
    justify-content: space-between;
}

.progress-step {
    font-size: 12px;
    color: #605E5C;
    transition: color 0.3s ease;
}

.progress-step.active {
    color: #0078D4;
    font-weight: 600;
}

.progress-step.completed {
    color: #107C10;
}

/* 成功状态样式 */
.success-animation-container {
    animation: successSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes successSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.success-message {
    font-size: 24px;
    font-weight: 600;
    color: #323130;
    margin: 16px 0 24px;
    animation: successTextSlide 0.6s ease 0.3s both;
}

@keyframes successTextSlide {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* License信息卡片 */
.license-info-card, .license-status-card {
    background: #FFFFFF;
    border: 1px solid #E1DFDD;
    border-radius: 12px;
    padding: 24px;
    margin: 24px 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.license-info-card::before, .license-status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #107C10;
}

.license-status-card.trial::before {
    background: #FFB900;
}

.license-info-card:hover, .license-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* License类型徽章 */
.license-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 16px;
}

.license-type-badge.trial {
    background: rgba(255, 185, 0, 0.1);
    color: #FFB900;
    border: 1px solid rgba(255, 185, 0, 0.3);
}

.license-type-badge.standard {
    background: rgba(0, 120, 212, 0.1);
    color: #0078D4;
    border: 1px solid rgba(0, 120, 212, 0.3);
}

.license-type-badge.professional {
    background: rgba(16, 124, 16, 0.1);
    color: #107C10;
    border: 1px solid rgba(16, 124, 16, 0.3);
}

/* License详情 */
.license-details, .license-status-details {
    margin-bottom: 16px;
}

.license-detail-item, .status-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #F3F2F1;
}

.license-detail-item:last-child, .status-detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 14px;
    color: #605E5C;
}

.detail-value {
    font-size: 14px;
    font-weight: 500;
    color: #323130;
}

.detail-value.valid {
    color: #107C10;
}

.detail-value.expired {
    color: #D13438;
}

/* 功能限制 */
.feature-restrictions {
    margin-top: 20px;
}

.feature-restrictions h4 {
    font-size: 14px;
    font-weight: 600;
    color: #323130;
    margin-bottom: 12px;
}

.restriction-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.restriction-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #605E5C;
}

.feature-restriction-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    margin-right: 8px;
    flex-shrink: 0;
}

.feature-restriction-icon.available {
    background: #107C10;
    color: white;
}

.feature-restriction-icon.restricted {
    background: #FFB900;
    color: white;
}

.feature-restriction-icon.unavailable {
    background: #D13438;
    color: white;
}

/* 按钮样式 */
.proceed-button, .upgrade-button, .refresh-button {
    background: linear-gradient(135deg, #0078D4, #40E0FF);
    color: #FFFFFF;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 120px;
}

.proceed-button:hover, .upgrade-button:hover, .refresh-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 120, 212, 0.3);
}

.retry-button, .support-contact-button {
    background: transparent;
    color: #0078D4;
    border: 1px solid #E1DFDD;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.retry-button:hover, .support-contact-button:hover {
    background: #F3F2F1;
    border-color: #0078D4;
    transform: translateY(-1px);
}

.action-buttons, .status-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
}

/* 错误容器 */
.error-container {
    background: rgba(209, 52, 56, 0.1);
    border: 1px solid rgba(209, 52, 56, 0.3);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    animation: errorSlideIn 0.4s ease;
}

@keyframes errorSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.error-message {
    color: #D13438;
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 8px;
}

.error-details {
    color: #605E5C;
    font-size: 14px;
    line-height: 1.4;
}

/* 升级提示 */
.upgrade-prompt {
    background: linear-gradient(135deg, #0078D4, #40E0FF);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
    animation: upgradePromptSlide 0.6s ease;
}

@keyframes upgradePromptSlide {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.upgrade-prompt::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: upgradeShimmer 3s infinite;
}

@keyframes upgradeShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.upgrade-prompt h3 {
    font-size: 18px;
    margin-bottom: 8px;
}

.upgrade-prompt p {
    font-size: 14px;
    margin-bottom: 16px;
    opacity: 0.9;
}

.upgrade-button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.upgrade-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 自动跳转 */
.auto-redirect {
    margin-top: 20px;
    font-size: 14px;
    color: #605E5C;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.skip-countdown-btn {
    background: #0078D4;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.skip-countdown-btn:hover {
    background: #005A9E;
    transform: scale(1.05);
}

/* 底部信息 */
.bottom-info {
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 16px 24px;
}

.info-links {
    display: flex;
    justify-content: center;
    gap: 24px;
}

.info-link {
    background: none;
    border: none;
    color: #0078D4;
    font-size: 13px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.info-link:hover {
    background: rgba(0, 120, 212, 0.1);
    transform: translateY(-1px);
}

/* 模态对话框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #E1DFDD;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #323130;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #605E5C;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #F3F2F1;
    color: #323130;
}

.modal-body {
    padding: 20px 24px;
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #E1DFDD;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.modal-button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-button.primary {
    background: #0078D4;
    color: white;
    border: none;
}

.modal-button.primary:hover {
    background: #005A9E;
}

.modal-button.secondary {
    background: transparent;
    color: #0078D4;
    border: 1px solid #E1DFDD;
}

.modal-button.secondary:hover {
    background: #F3F2F1;
    border-color: #0078D4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .license-activation-container {
        margin: 0;
        border-radius: 0;
    }

    .main-content {
        padding: 24px 16px;
    }

    .nav-content {
        padding: 0 8px;
    }

    .nav-title {
        margin: 0 16px;
    }

    .nav-title h1 {
        font-size: 18px;
    }

    .activation-code-input {
        font-size: 16px;
        height: 48px;
    }

    .license-info-card, .license-status-card {
        padding: 16px;
    }

    .action-buttons, .status-actions {
        flex-direction: column;
    }

    .info-links {
        flex-direction: column;
        gap: 12px;
    }
}

@media (max-height: 600px) {
    .main-content {
        padding: 20px 24px;
    }

    .license-status-icon {
        width: 60px;
        height: 60px;
        font-size: 30px;
        margin-bottom: 16px;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .activation-code-input {
        border-width: 3px;
    }

    .license-info-card, .license-status-card {
        border-width: 2px;
    }

    .nav-button {
        border-width: 2px;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}