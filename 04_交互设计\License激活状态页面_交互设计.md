# License激活/状态页面 - 交互设计规范

## 1. 交互流程设计

### 1.1 主要交互流程
```
从启动欢迎页面跳转进入
    ↓
接收激活上下文信息 (失败原因、设备指纹等)
    ↓
根据失败原因显示相应界面状态:
    ├── 首次激活 → 显示欢迎信息 + 激活码输入
    ├── License过期 → 显示过期提示 + 重新激活
    └── 设备不匹配 → 显示设备提示 + 重新激活
    ↓
用户输入激活码:
    ├── 实时格式验证 (XXXXX-XXXXX-XXXXX-XXXXX-XXXXX)
    ├── 字符过滤 (只允许字母数字)
    └── 自动格式化显示
    ↓
点击激活按钮:
    ├── 显示激活进度动画
    ├── 调用License验证服务
    └── 根据结果分支处理
    ↓
激活结果处理:
    ├── 成功 → 显示成功界面 → 3秒后自动跳转主界面
    └── 失败 → 显示错误信息 → 提供重试选项
```

### 1.2 License状态管理流程
```
已激活用户直接进入状态管理界面
    ↓
显示当前License信息:
    ├── License类型和状态
    ├── 到期时间 (如适用)
    ├── 权限级别说明
    └── 功能限制列表
    ↓
根据License类型提供操作:
    ├── 试用版 → 显示升级选项和功能对比
    ├── 标准版 → 显示升级到专业版选项
    └── 专业版 → 显示续期提醒 (如临近到期)
    ↓
用户操作选择:
    ├── 升级License → 显示升级指导
    ├── 刷新状态 → 重新验证License
    ├── 联系支持 → 显示技术支持信息
    └── 进入主界面 → 跳转到主应用程序
```

## 2. 交互状态设计

### 2.1 页面状态定义
```typescript
enum PageInteractionState {
    Loading = "loading",                    // 页面加载中
    ActivationInput = "activation_input",   // 激活码输入状态
    Activating = "activating",             // 正在激活
    ActivationSuccess = "activation_success", // 激活成功
    ActivationFailed = "activation_failed", // 激活失败
    LicenseStatus = "license_status",       // License状态管理
    UpgradePrompt = "upgrade_prompt",       // 升级提示
    ContactSupport = "contact_support"      // 联系支持
}

interface InteractionContext {
    currentState: PageInteractionState;
    previousState: PageInteractionState;
    transitionDuration: number;
    userAction: string;
    errorMessage?: string;
    successMessage?: string;
}
```

### 2.2 激活码输入交互状态
```typescript
interface ActivationCodeState {
    value: string;
    isValid: boolean;
    isFormatted: boolean;
    errorMessage: string;
    validationRules: {
        length: boolean;        // 长度验证
        characters: boolean;    // 字符验证
        format: boolean;        // 格式验证
    };
}
```

## 3. 用户交互元素设计

### 3.1 激活码输入交互
```css
/* 激活码输入框交互状态 */
.activation-code-input {
    transition: all 0.3s ease;
    position: relative;
}

.activation-code-input:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 4px rgba(0, 120, 212, 0.2);
}

.activation-code-input.typing {
    border-color: var(--primary-color);
    background: rgba(0, 120, 212, 0.05);
}

.activation-code-input.valid {
    border-color: var(--success-color);
    background: rgba(16, 124, 16, 0.05);
}

.activation-code-input.invalid {
    border-color: var(--error-color);
    background: rgba(209, 52, 56, 0.05);
    animation: inputError 0.5s ease;
}

@keyframes inputError {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 实时字符计数器 */
.character-counter {
    position: absolute;
    bottom: -20px;
    right: 0;
    font-size: 12px;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.character-counter.complete {
    color: var(--success-color);
    font-weight: 600;
}
```

### 3.2 激活按钮交互状态
```css
.activation-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.activation-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 120, 212, 0.3);
}

.activation-button:active:not(:disabled) {
    transform: translateY(0);
    transition-duration: 0.1s;
}

.activation-button.loading {
    pointer-events: none;
    color: transparent;
}

.activation-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: buttonSpinner 1s linear infinite;
}

@keyframes buttonSpinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮成功状态 */
.activation-button.success {
    background: var(--success-color);
    animation: buttonSuccess 0.6s ease;
}

@keyframes buttonSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
```

### 3.3 设备指纹复制交互
```css
.device-fingerprint-container {
    position: relative;
    transition: all 0.3s ease;
}

.device-fingerprint-container:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
}

.copy-fingerprint-btn {
    opacity: 0.7;
    transition: all 0.2s ease;
}

.copy-fingerprint-btn:hover {
    opacity: 1;
    transform: scale(1.05);
}

.copy-fingerprint-btn.copied {
    background: var(--success-color);
    color: white;
    animation: copySuccess 2s ease;
}

@keyframes copySuccess {
    0% { background: var(--success-color); }
    50% { background: var(--success-color); }
    100% { background: transparent; }
}

/* 复制成功提示 */
.copy-tooltip {
    position: absolute;
    top: -30px;
    right: 0;
    background: var(--text-primary);
    color: var(--text-on-primary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transform: translateY(5px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.copy-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}
```

## 4. 动画和过渡效果

### 4.1 页面状态切换动画
```css
.page-transition-container {
    position: relative;
    overflow: hidden;
}

.page-state {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 淡入淡出过渡 */
.fade-transition-enter {
    opacity: 0;
    transform: translateY(20px);
}

.fade-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
}

.fade-transition-exit {
    opacity: 1;
    transform: translateY(0);
}

.fade-transition-exit-active {
    opacity: 0;
    transform: translateY(-20px);
}

/* 滑动过渡 */
.slide-transition-enter {
    transform: translateX(100%);
}

.slide-transition-enter-active {
    transform: translateX(0);
}

.slide-transition-exit {
    transform: translateX(0);
}

.slide-transition-exit-active {
    transform: translateX(-100%);
}
```

### 4.2 激活进度动画
```css
.activation-progress-container {
    width: 100%;
    margin: 20px 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activation-progress-container.active {
    opacity: 1;
}

.activation-progress-bar {
    height: 4px;
    background: var(--progress-bg);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.activation-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 2px;
    width: 0%;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.activation-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 进度步骤指示器 */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

.progress-step {
    font-size: 12px;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.progress-step.active {
    color: var(--primary-color);
    font-weight: 600;
}

.progress-step.completed {
    color: var(--success-color);
}
```

### 4.3 成功状态动画
```css
.success-animation-container {
    text-align: center;
    animation: successSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes successSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.success-icon {
    font-size: 64px;
    color: var(--success-color);
    animation: successIconPulse 0.8s ease;
}

@keyframes successIconPulse {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.success-message {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 16px 0;
    animation: successTextSlide 0.6s ease 0.3s both;
}

@keyframes successTextSlide {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
```

## 5. 响应式交互设计

### 5.1 触控设备交互优化
```css
@media (pointer: coarse) {
    .activation-code-input {
        height: 56px;
        font-size: 18px;
        padding: 0 16px;
    }
    
    .activation-button {
        min-height: 48px;
        padding: 16px 24px;
    }
    
    .copy-fingerprint-btn {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
    }
    
    /* 增加触控目标大小 */
    .interactive-element {
        min-height: 44px;
        min-width: 44px;
    }
}
```

### 5.2 键盘导航优化
```css
.keyboard-navigation-active .focusable:focus {
    outline: 3px solid var(--border-focus);
    outline-offset: 2px;
    z-index: 1;
}

/* Tab顺序指示 */
.tab-order-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.keyboard-navigation-active .tab-order-indicator {
    opacity: 1;
}
```

## 6. 错误处理交互

### 6.1 错误状态动画
```css
.error-container {
    background: rgba(209, 52, 56, 0.1);
    border: 1px solid rgba(209, 52, 56, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    animation: errorSlideIn 0.4s ease;
}

@keyframes errorSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-icon {
    color: var(--error-color);
    font-size: 24px;
    animation: errorIconShake 0.5s ease;
}

@keyframes errorIconShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

.error-message {
    color: var(--error-color);
    font-weight: 500;
    margin: 8px 0;
}

.retry-button {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-button:hover {
    background: var(--error-color);
    filter: brightness(1.1);
    transform: translateY(-1px);
}
```

### 6.2 实时验证反馈
```css
.validation-feedback {
    font-size: 12px;
    margin-top: 4px;
    transition: all 0.3s ease;
    min-height: 16px;
}

.validation-feedback.error {
    color: var(--error-color);
    animation: validationError 0.3s ease;
}

.validation-feedback.success {
    color: var(--success-color);
    animation: validationSuccess 0.3s ease;
}

@keyframes validationError {
    0% { opacity: 0; transform: translateY(-5px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes validationSuccess {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
}
```

## 7. 用户引导交互

### 7.1 首次使用引导
```css
.onboarding-tooltip {
    position: absolute;
    background: var(--text-primary);
    color: var(--text-on-primary);
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    max-width: 250px;
    z-index: 1000;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.onboarding-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.onboarding-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 20px;
    border: 8px solid transparent;
    border-top-color: var(--text-primary);
}

.onboarding-highlight {
    position: relative;
    z-index: 999;
}

.onboarding-highlight::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    animation: highlightPulse 2s infinite;
}

@keyframes highlightPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.02); }
}
```

### 7.2 升级提示交互
```css
.upgrade-prompt {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-radius: 12px;
    padding: 24px;
    margin: 16px 0;
    position: relative;
    overflow: hidden;
    animation: upgradePromptSlide 0.6s ease;
}

@keyframes upgradePromptSlide {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.upgrade-prompt::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: upgradeShimmer 3s infinite;
}

@keyframes upgradeShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
```

## 8. 性能优化交互

### 8.1 防抖和节流
```typescript
// 激活码输入防抖
const debouncedValidation = debounce((code: string) => {
    validateActivationCode(code);
}, 300);

// 激活按钮防重复点击
const throttledActivation = throttle(() => {
    activateLicense();
}, 2000);
```

### 8.2 动画性能优化
```css
.performance-optimized {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.gpu-accelerated {
    transform: translate3d(0, 0, 0);
}
```

## 9. 无障碍交互设计

### 9.1 屏幕阅读器支持
```html
<!-- ARIA标签示例 -->
<div role="form" aria-labelledby="activation-title">
    <h2 id="activation-title">License激活</h2>
    <input type="text" 
           aria-label="License激活码"
           aria-describedby="code-help code-error"
           aria-invalid="false">
    <div id="code-help">请输入20位激活码</div>
    <div id="code-error" aria-live="polite"></div>
</div>
```

### 9.2 键盘快捷键
```typescript
// 键盘事件处理
document.addEventListener('keydown', (e) => {
    switch(e.key) {
        case 'Enter':
            if (isActivationCodeValid) {
                activateLicense();
            }
            break;
        case 'Escape':
            clearActivationCode();
            break;
        case 'F1':
            showHelp();
            break;
    }
});
```

## 10. 下一步设计重点
1. **HTML原型制作**：制作像素级精确的交互原型
2. **XAML资源生成**：生成WPF XAML样式和模板
3. **用户测试验证**：验证交互设计的可用性和易用性
