<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商用HVAC空调监控调试软件 - 启动欢迎页面</title>
    <link rel="stylesheet" href="css/startup-welcome.css">
    <link rel="stylesheet" href="css/fluent-design.css">
</head>
<body>
    <div class="startup-container" id="startupContainer">
        <!-- 顶部品牌区域 -->
        <header class="brand-header">
            <div class="brand-content">
                <div class="company-logo">
                    <svg class="logo-icon" viewBox="0 0 48 48">
                        <defs>
                            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#0078D4"/>
                                <stop offset="100%" style="stop-color:#FF8C00"/>
                            </linearGradient>
                        </defs>
                        <rect width="48" height="48" rx="8" fill="url(#logoGradient)"/>
                        <path d="M12 16h24v4H12zm0 8h24v4H12zm0 8h16v4H12z" fill="white"/>
                    </svg>
                </div>
                <div class="brand-info">
                    <h1 class="app-title">商用HVAC空调监控调试软件</h1>
                    <p class="company-name">专业暖通空调系统解决方案</p>
                </div>
                <div class="version-info">
                    <span class="version-label">版本</span>
                    <span class="version-number" id="versionNumber">1.0.0.0</span>
                </div>
            </div>
        </header>

        <!-- 中央状态区域 -->
        <main class="main-content">
            <div class="app-icon-container">
                <div class="app-icon" id="appIcon">
                    <svg class="hvac-icon" viewBox="0 0 128 128">
                        <defs>
                            <linearGradient id="hvacGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#0078D4"/>
                                <stop offset="50%" style="stop-color:#40E0FF"/>
                                <stop offset="100%" style="stop-color:#FF8C00"/>
                            </linearGradient>
                        </defs>
                        <circle cx="64" cy="64" r="56" fill="url(#hvacGradient)" opacity="0.1"/>
                        <circle cx="64" cy="64" r="48" fill="none" stroke="url(#hvacGradient)" stroke-width="3"/>
                        
                        <!-- 空调图标 -->
                        <rect x="32" y="48" width="64" height="32" rx="4" fill="url(#hvacGradient)"/>
                        <rect x="36" y="52" width="56" height="24" rx="2" fill="white"/>
                        
                        <!-- 风扇叶片 -->
                        <g transform="translate(64,64)">
                            <path d="M-8,-8 L8,8 M8,-8 L-8,8" stroke="#0078D4" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="0" cy="0" r="3" fill="#0078D4"/>
                        </g>
                        
                        <!-- 温度指示 -->
                        <circle cx="48" cy="40" r="4" fill="#FF8C00"/>
                        <circle cx="80" cy="40" r="4" fill="#40E0FF"/>
                    </svg>
                </div>
            </div>

            <div class="status-section">
                <div class="status-message" id="statusMessage">
                    正在启动系统...
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text">
                        <span class="progress-percentage" id="progressPercentage">0%</span>
                    </div>
                </div>

                <div class="detailed-status" id="detailedStatus">
                    <div class="status-item" id="fingerprintStatus">
                        <span class="status-icon" id="fingerprintIcon">⏳</span>
                        <span class="status-text">正在生成设备指纹...</span>
                    </div>
                    <div class="status-item" id="licenseStatus">
                        <span class="status-icon" id="licenseIcon">⏳</span>
                        <span class="status-text">等待License验证...</span>
                    </div>
                </div>

                <div class="device-info" id="deviceInfo" style="display: none;">
                    <div class="info-label">设备指纹:</div>
                    <div class="fingerprint-display" id="fingerprintDisplay">生成中...</div>
                </div>
            </div>

            <!-- 错误状态区域 -->
            <div class="error-section" id="errorSection" style="display: none;">
                <div class="error-icon">⚠️</div>
                <div class="error-message" id="errorMessage">初始化过程中发生错误</div>
                <div class="error-details" id="errorDetails">
                    <div class="error-toggle" id="errorToggle">查看详情 ▼</div>
                    <div class="error-content" id="errorContent">
                        错误详细信息将在这里显示...
                    </div>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-buttons" id="actionButtons" style="display: none;">
                <button class="button-secondary" id="retryButton">
                    <span class="button-icon">🔄</span>
                    重试
                </button>
                <button class="button-secondary" id="exitButton">
                    <span class="button-icon">❌</span>
                    退出
                </button>
            </div>

            <!-- 成功状态区域 -->
            <div class="success-section" id="successSection" style="display: none;">
                <div class="success-icon">✅</div>
                <div class="success-message" id="successMessage">初始化完成</div>
                <div class="license-info" id="licenseInfo">
                    <div class="license-type" id="licenseType">License类型: 专业版</div>
                    <div class="permission-level" id="permissionLevel">权限级别: 研发人员权限</div>
                </div>
                <div class="auto-redirect" id="autoRedirect">
                    <span>3秒后自动进入主界面...</span>
                </div>
            </div>
        </main>

        <!-- 底部信息区域 -->
        <footer class="footer-info">
            <div class="copyright">
                <span id="copyrightText">© 2024 您的公司名称. 保留所有权利.</span>
            </div>
            <div class="footer-buttons">
                <button class="button-text" id="aboutButton">关于</button>
                <button class="button-text" id="helpButton">帮助</button>
            </div>
        </footer>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <script src="js/startup-welcome.js"></script>
</body>
</html>
